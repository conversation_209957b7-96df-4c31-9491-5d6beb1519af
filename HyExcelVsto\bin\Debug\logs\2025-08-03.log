﻿2025-08-03 11:15:55 [INFO] 显示设置已变更
2025-08-03 11:15:56 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 11:15:57 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 111876918
2025-08-03 11:15:57 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 111876918)
2025-08-03 11:15:57 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 11:15:57 [INFO] 显示设置变更后TopForm关系已重建
2025-08-03 12:36:08 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:36:08 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 111876918, 新父窗口: 7742166
2025-08-03 12:36:08 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 12:36:08 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:36:08 [INFO] App_WorkbookActivate: 工作簿 '一点一案上传方案信息表-20250801-170610.xlsx' 激活处理完成
2025-08-03 12:36:08 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:36:08 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-03 12:36:08 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:36:08 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 12:36:08 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:36:08 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 12:36:08 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 12:36:08 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:36:08 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 12:36:08 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:36:09 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 12:36:09 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 12:36:09 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:36:09 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:36:09 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 7742166, 新父窗口: 111876918
2025-08-03 12:36:09 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 111876918)
2025-08-03 12:36:09 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:36:09 [INFO] App_WorkbookActivate: 工作簿 '场景清单_专用场景_ad60d545-8ea4-4b24-bd64-e2b8352ed1a1.csv' 激活处理完成
2025-08-03 12:36:09 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:36:09 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 111876918)
2025-08-03 12:36:09 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:36:09 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 12:36:10 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:36:10 [WARN] 检测到Excel窗口句柄变化: 7742166 -> 111876918
2025-08-03 12:36:10 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 111876918
2025-08-03 12:36:10 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:36:10 [ERROR] SetTop: 设置父窗口关系失败 - 期望: 111876918, 实际: 65552
2025-08-03 12:36:10 [WARN] 父子窗口关系不正确. 当前父窗口: 65552, 期望父窗口: 111876918
2025-08-03 12:36:10 [WARN] 检测到父子窗口关系异常，尝试修复
2025-08-03 12:36:10 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 111876918
2025-08-03 12:36:10 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 111876918)
2025-08-03 12:36:10 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 111876918)
2025-08-03 12:36:10 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 111876918)
2025-08-03 12:36:10 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:36:10 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 12:36:10 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:50:50 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-08-03 12:50:50 [INFO] 系统事件监控已停止
2025-08-03 12:50:50 [INFO] Excel窗口句柄监控已停止
2025-08-03 12:50:50 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-08-03 12:50:51 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:50:51 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 12:50:51 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 12:50:51 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 7742166
2025-08-03 12:50:51 [INFO] 系统事件监控已启动
2025-08-03 12:50:51 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:50:51 [INFO] App_WorkbookActivate: 工作簿 '一点一案上传方案信息表-20250801-170610.xlsx' 激活处理完成
2025-08-03 12:50:51 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:50:51 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-03 12:50:51 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:50:51 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 12:50:52 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:50:52 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 12:50:52 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 12:50:52 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:50:52 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 12:50:52 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:50:52 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 12:50:52 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 12:50:52 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:51:29 [INFO] App_WorkbookOpen: 工作簿 '2CC扩容节点清单(2).xlsx' 打开事件触发
2025-08-03 12:51:29 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:51:29 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 7742166, 新父窗口: 89790590
2025-08-03 12:51:29 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 89790590)
2025-08-03 12:51:29 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:51:29 [INFO] App_WorkbookOpen: 工作簿 '2CC扩容节点清单(2).xlsx' 打开处理完成
2025-08-03 12:51:29 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:51:29 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 89790590)
2025-08-03 12:51:29 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:51:29 [INFO] App_WorkbookActivate: 工作簿 '2CC扩容节点清单(2).xlsx' 激活处理完成
2025-08-03 12:51:29 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 12:51:29 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 89790590)
2025-08-03 12:51:29 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 12:51:29 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 12:51:30 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:51:30 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 89790590
2025-08-03 12:51:30 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:51:30 [ERROR] SetTop: 设置父窗口关系失败 - 期望: 89790590, 实际: 65552
2025-08-03 12:51:30 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:51:30 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 12:51:30 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 89790590
2025-08-03 12:51:30 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 89790590)
2025-08-03 12:51:30 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:51:30 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 12:51:30 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 89790590
2025-08-03 12:51:30 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 89790590)
2025-08-03 12:51:30 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 12:51:30 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-08-03 13:04:43 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-08-03 13:04:43 [INFO] 系统事件监控已停止
2025-08-03 13:04:43 [INFO] Excel窗口句柄监控已停止
2025-08-03 13:04:43 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-08-03 13:04:44 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 13:04:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 13:04:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 13:04:44 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 7742166
2025-08-03 13:04:44 [INFO] 系统事件监控已启动
2025-08-03 13:04:44 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 13:04:44 [INFO] App_WorkbookActivate: 工作簿 '一点一案上传方案信息表-20250801-170610.xlsx' 激活处理完成
2025-08-03 13:04:44 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 13:04:44 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-03 13:04:44 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 13:04:44 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 13:04:45 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 13:04:45 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 13:04:45 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 13:04:45 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 13:04:45 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 13:04:45 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 13:04:45 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 13:04:45 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 13:04:45 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 13:32:28 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 13:32:28 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-03 13:32:28 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 14:30:37 [INFO] 用户取消了文件选择
2025-08-03 14:30:40 [INFO] 用户取消了文件选择
2025-08-03 14:30:41 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-03 14:30:41 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-03 14:30:41 [INFO] 开始显示窗体 'Excel文件记录管理（双击文件名打开文件）'，位置模式: Center
2025-08-03 14:30:41 [INFO] 窗体 'Excel文件记录管理（双击文件名打开文件）' 以TopMostForm为父窗体显示
2025-08-03 14:30:41 [INFO] 窗体 'Excel文件记录管理（双击文件名打开文件）' 显示完成，句柄: 38014364
2025-08-03 14:30:41 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 打开成功
2025-08-03 14:33:58 [INFO] Excel窗口句柄监控器初始化完成
2025-08-03 14:33:59 [INFO] 配置文件实例已在加载时初始化
2025-08-03 14:33:59 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-08-03 14:33:59 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-08-03 14:33:59 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-08-03 14:33:59 [INFO] 成功初始化Excel应用程序实例
2025-08-03 14:33:59 [INFO] 自动备份路径未配置
2025-08-03 14:33:59 [DEBUG] 开始初始化授权控制器
2025-08-03 14:33:59 [DEBUG] 授权系统初始化完成，耗时: 479ms
2025-08-03 14:33:59 [DEBUG] 开始初始化授权验证
2025-08-03 14:33:59 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-08-03 14:33:59 [DEBUG] 权限管理器初始化成功
2025-08-03 14:33:59 [DEBUG] 使用新的权限管理器进行初始化
2025-08-03 14:33:59 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-03 14:33:59 [INFO] 开始初始化UI权限管理
2025-08-03 14:33:59 [DEBUG] [实例ID: 6b0d571a] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-03 14:33:59 [DEBUG] 🔍 [实例ID: 6b0d571a] 字典引用一致性检查:
2025-08-03 14:33:59 [DEBUG] 🔍   标题映射一致性: True
2025-08-03 14:33:59 [DEBUG] 🔍   权限映射一致性: True
2025-08-03 14:33:59 [DEBUG] 🔍   信息映射一致性: True
2025-08-03 14:33:59 [DEBUG] 🔍   特殊控件一致性: True
2025-08-03 14:33:59 [DEBUG] 控件权限管理器初始化完成 [实例ID: 6b0d571a]
2025-08-03 14:33:59 [DEBUG] 开始注册控件权限映射
2025-08-03 14:33:59 [INFO] 开始初始化全局控件映射
2025-08-03 14:33:59 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-08-03 14:33:59 [DEBUG] 开始生成控件标题映射
2025-08-03 14:33:59 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-08-03 14:33:59 [DEBUG] 通过反射获取到 124 个字段
2025-08-03 14:33:59 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:33:59 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-08-03 14:34:00 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-08-03 14:34:00 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-08-03 14:34:00 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-08-03 14:34:00 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-08-03 14:34:00 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 控件结构获取完成，共获取到 122 个控件
2025-08-03 14:34:00 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-03 14:34:00 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-08-03 14:34:00 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-08-03 14:34:00 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-08-03 14:34:00 [INFO] 控件标题映射生成完成，共生成 109 项映射
2025-08-03 14:34:00 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-03 14:34:00 [DEBUG] 全局控件标题映射生成完成，共生成 109 项
2025-08-03 14:34:00 [INFO] 关键控件标题映射: hyTab -> Develop
2025-08-03 14:34:00 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-08-03 14:34:00 [WARN] 关键控件未找到标题映射: buttonAbout
2025-08-03 14:34:00 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-08-03 14:34:00 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-08-03 14:34:00 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-08-03 14:34:00 [INFO] === znAbout控件标题映射诊断 ===
2025-08-03 14:34:00 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-08-03 14:34:00 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-08-03 14:34:00 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-08-03 14:34:00 [DEBUG] === 所有生成的控件标题映射 ===
2025-08-03 14:34:00 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-08-03 14:34:00 [DEBUG] 控件映射: btnAngleExtractor -> '方向角/下倾角提取'
2025-08-03 14:34:00 [DEBUG] 控件映射: btnStationConverter -> '站点系统数量统计'
2025-08-03 14:34:00 [DEBUG] 控件映射: btnStationDataProcessor -> '基站台账数据转换处理'
2025-08-03 14:34:00 [DEBUG] 控件映射: btnTowerAccountProcessor -> '铁塔内部台账转换工具'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn发送及存档 -> '临时/发送/存档'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规性检查'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-08-03 14:34:00 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-08-03 14:34:00 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: button10 -> '记录当前文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-08-03 14:34:00 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-08-03 14:34:00 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-08-03 14:34:00 [DEBUG] 控件映射: button14 -> '临时/发送/存档'
2025-08-03 14:34:00 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-08-03 14:34:00 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-08-03 14:34:00 [DEBUG] 控件映射: button17 -> '向下填充'
2025-08-03 14:34:00 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-08-03 14:34:00 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-08-03 14:34:00 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-08-03 14:34:00 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-08-03 14:34:00 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-08-03 14:34:00 [DEBUG] 控件映射: button3 -> '关于'
2025-08-03 14:34:00 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-08-03 14:34:00 [DEBUG] 控件映射: button5 -> '最近打开文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-08-03 14:34:00 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-08-03 14:34:00 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-08-03 14:34:00 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-08-03 14:34:00 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-08-03 14:34:00 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-08-03 14:34:00 [DEBUG] 控件映射: button9 -> '最近打开文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-08-03 14:34:00 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-08-03 14:34:00 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-08-03 14:34:00 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-08-03 14:34:00 [DEBUG] 控件映射: button打开文件 -> '打开文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: button多边形GPS坐标转换器 -> '多边形GPS坐标转换器'
2025-08-03 14:34:00 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-08-03 14:34:00 [DEBUG] 控件映射: button记录当前文件 -> '记录当前文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-08-03 14:34:00 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-08-03 14:34:00 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-08-03 14:34:00 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-08-03 14:34:00 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-08-03 14:34:00 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-08-03 14:34:00 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-08-03 14:34:00 [DEBUG] 控件映射: button铁塔KML点图转换 -> '铁塔KML点图转换'
2025-08-03 14:34:00 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-08-03 14:34:00 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-08-03 14:34:00 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-08-03 14:34:00 [DEBUG] 控件映射: button文件操作 -> '文件查找/复制/改名'
2025-08-03 14:34:00 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-08-03 14:34:00 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-08-03 14:34:00 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-08-03 14:34:00 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-08-03 14:34:00 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-08-03 14:34:00 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-08-03 14:34:00 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-08-03 14:34:00 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-08-03 14:34:00 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-08-03 14:34:00 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-08-03 14:34:00 [DEBUG] 控件映射: group1 -> '关于'
2025-08-03 14:34:00 [DEBUG] 控件映射: group2 -> '脚本'
2025-08-03 14:34:00 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-08-03 14:34:00 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-08-03 14:34:00 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-08-03 14:34:00 [DEBUG] 控件映射: group文件 -> '文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: group无线 -> '无线'
2025-08-03 14:34:00 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-08-03 14:34:00 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-08-03 14:34:00 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-08-03 14:34:00 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-08-03 14:34:00 [DEBUG] 控件映射: menu1 -> '基站数据处理'
2025-08-03 14:34:00 [DEBUG] 控件映射: menu2 -> '其它'
2025-08-03 14:34:00 [DEBUG] 控件映射: menu3 -> '设置'
2025-08-03 14:34:00 [DEBUG] 控件映射: menu5 -> '修复'
2025-08-03 14:34:00 [DEBUG] 控件映射: menuHY -> '其它'
2025-08-03 14:34:00 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-08-03 14:34:00 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-08-03 14:34:00 [DEBUG] 控件映射: menu修复 -> '修复'
2025-08-03 14:34:00 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-08-03 14:34:00 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-08-03 14:34:00 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-08-03 14:34:00 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-08-03 14:34:00 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-08-03 14:34:00 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-08-03 14:34:00 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-08-03 14:34:00 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-08-03 14:34:00 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-08-03 14:34:00 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-08-03 14:34:00 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-08-03 14:34:00 [DEBUG] 开始生成控件权限映射
2025-08-03 14:34:00 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-08-03 14:34:00 [DEBUG] 通过反射获取到 124 个字段
2025-08-03 14:34:00 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-08-03 14:34:00 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-08-03 14:34:00 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-08-03 14:34:00 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-08-03 14:34:00 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-08-03 14:34:00 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-08-03 14:34:00 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 14:34:00 [INFO] 控件结构获取完成，共获取到 122 个控件
2025-08-03 14:34:00 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-03 14:34:00 [INFO] 控件权限映射生成完成，共生成 116 项映射
2025-08-03 14:34:00 [DEBUG] 全局控件权限映射生成完成，共生成 116 项
2025-08-03 14:34:00 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-08-03 14:34:00 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-08-03 14:34:00 [INFO] 全局控件映射初始化完成 - 标题映射: 109 项, 权限映射: 116 项
2025-08-03 14:34:00 [DEBUG] 批量注册控件权限映射完成，成功: 116/116
2025-08-03 14:34:00 [DEBUG] HyExcel控件权限映射注册完成，共注册 116 个控件
2025-08-03 14:34:00 [INFO] 开始初始化权限验证
2025-08-03 14:34:00 [DEBUG] 设置默认UI可见性为false
2025-08-03 14:34:00 [DEBUG] 开始检查所有需要的权限
2025-08-03 14:34:00 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-08-03 14:34:01 [INFO] 启动网络授权信息获取任务
2025-08-03 14:34:01 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-08-03 14:34:01 [INFO] 所有权限检查完成
2025-08-03 14:34:01 [DEBUG] 应用权限状态到UI控件
2025-08-03 14:34:01 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:34:01 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:01 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:34:01 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:01 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:34:01 [DEBUG] 启动后台权限刷新任务
2025-08-03 14:34:01 [DEBUG] 启动延迟权限刷新任务
2025-08-03 14:34:01 [INFO] 权限验证初始化完成
2025-08-03 14:34:01 [INFO] UI权限管理初始化完成
2025-08-03 14:34:01 [INFO] 收到权限管理器初始化完成通知
2025-08-03 14:34:01 [INFO] 开始刷新控件标题
2025-08-03 14:34:01 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 14:34:01 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 14:34:01 [DEBUG] 控件标题刷新完成
2025-08-03 14:34:01 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 14:34:01 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 14:34:01 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 14:34:01 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:34:01 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 14:34:01 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:01 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 14:34:01 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:01 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 14:34:01 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:01 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 14:34:01 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:34:01 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 14:34:01 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 14:34:01 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 14:34:01 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 14:34:01 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 14:34:01 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 14:34:01 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 14:34:01 [INFO] 控件标题更正完成
2025-08-03 14:34:01 [INFO] 控件标题刷新完成
2025-08-03 14:34:01 [INFO] 权限管理器初始化完成处理结束
2025-08-03 14:34:01 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-03 14:34:01 [DEBUG] 授权验证初始化完成
2025-08-03 14:34:01 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-08-03 14:34:01 [INFO] 成功加载配置和授权信息
2025-08-03 14:34:01 [INFO] 开始初始化定时器和设置
2025-08-03 14:34:01 [INFO] 定时器和设置初始化完成
2025-08-03 14:34:01 [INFO] 开始VSTO插件启动流程
2025-08-03 14:34:01 [INFO] TopMostForm窗体加载完成
2025-08-03 14:34:01 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 14:34:01 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 12329192
2025-08-03 14:34:01 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 12329192)
2025-08-03 14:34:01 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 12329192
2025-08-03 14:34:01 [INFO] 系统事件监控已启动
2025-08-03 14:34:01 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 14:34:01 [INFO] OpenForm: 窗体标题已设置为类名 'CrosshairOverlayForm'
2025-08-03 14:34:01 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-08-03 14:34:01 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-08-03 14:34:01 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-08-03 14:34:01 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 16066100
2025-08-03 14:34:01 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-08-03 14:34:01 [INFO] VSTO插件启动流程完成
2025-08-03 14:34:02 [INFO] 从Remote成功获取到网络授权信息
2025-08-03 14:34:02 [INFO] 网络授权信息已更新并触发回调
2025-08-03 14:34:02 [INFO] 网络授权信息已从 Network 更新
2025-08-03 14:34:02 [INFO] 授权版本: 1.0
2025-08-03 14:34:02 [INFO] 颁发者: ExtensionsTools
2025-08-03 14:34:02 [INFO] 用户数量: 3
2025-08-03 14:34:02 [INFO] 分组权限数量: 2
2025-08-03 14:34:02 [WARN] 配置文件中未找到用户组信息
2025-08-03 14:34:02 [INFO] 已重新设置用户组: []
2025-08-03 14:34:02 [INFO] 用户组信息已重新设置
2025-08-03 14:34:02 [INFO] 立即刷新权限缓存和UI界面
2025-08-03 14:34:02 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-03 14:34:02 [DEBUG] 使用新的权限管理器进行强制刷新
2025-08-03 14:34:02 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-08-03 14:34:02 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-03 14:34:02 [DEBUG] 本地权限缓存已清空
2025-08-03 14:34:02 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-08-03 14:34:02 [INFO] 所有权限检查完成
2025-08-03 14:34:02 [DEBUG] 权限重新检查完成
2025-08-03 14:34:02 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:34:02 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:02 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:34:02 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:02 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:34:02 [INFO] UI界面权限状态已更新
2025-08-03 14:34:02 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 14:34:02 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 14:34:02 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-08-03 14:34:02 [INFO] 权限缓存和UI界面立即刷新完成
2025-08-03 14:34:02 [INFO] 网络授权已更新，开始刷新控件标题
2025-08-03 14:34:02 [INFO] 开始刷新Ribbon控件标题
2025-08-03 14:34:02 [DEBUG] 权限缓存已清空，清除了 116 个缓存项
2025-08-03 14:34:02 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-08-03 14:34:02 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 14:34:02 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 14:34:02 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 14:34:02 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 14:34:02 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 14:34:02 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:02 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:34:02 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 14:34:02 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 14:34:02 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 14:34:02 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 14:34:02 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 14:34:02 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 14:34:02 [INFO] 控件标题更正完成
2025-08-03 14:34:02 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-08-03 14:34:02 [INFO] Ribbon控件标题刷新完成
2025-08-03 14:34:02 [INFO] 控件标题刷新完成
2025-08-03 14:34:02 [DEBUG] Ribbon控件标题已刷新
2025-08-03 14:34:02 [INFO] 开始刷新控件标题
2025-08-03 14:34:02 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 14:34:02 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 14:34:02 [DEBUG] 控件标题刷新完成
2025-08-03 14:34:02 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 14:34:02 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 14:34:02 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 14:34:02 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 14:34:02 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 14:34:02 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:02 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 14:34:02 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:34:02 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 14:34:02 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 14:34:02 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 14:34:02 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 14:34:02 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 14:34:02 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 14:34:02 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 14:34:02 [INFO] 控件标题更正完成
2025-08-03 14:34:02 [INFO] 控件标题刷新完成
2025-08-03 14:34:02 [DEBUG] Ribbon控件标题已立即刷新
2025-08-03 14:34:02 [INFO] 开始刷新授权状态
2025-08-03 14:34:02 [DEBUG] 开始初始化授权验证
2025-08-03 14:34:02 [DEBUG] 使用新的权限管理器进行初始化
2025-08-03 14:34:02 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-03 14:34:02 [INFO] 开始初始化UI权限管理
2025-08-03 14:34:02 [DEBUG] [实例ID: 1208ccfd] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-03 14:34:02 [DEBUG] 🔍 [实例ID: 1208ccfd] 字典引用一致性检查:
2025-08-03 14:34:02 [DEBUG] 🔍   标题映射一致性: True
2025-08-03 14:34:02 [DEBUG] 🔍   权限映射一致性: True
2025-08-03 14:34:02 [DEBUG] 🔍   信息映射一致性: True
2025-08-03 14:34:02 [DEBUG] 🔍   特殊控件一致性: True
2025-08-03 14:34:02 [DEBUG] 控件权限管理器初始化完成 [实例ID: 1208ccfd]
2025-08-03 14:34:02 [DEBUG] 开始注册控件权限映射
2025-08-03 14:34:02 [DEBUG] 批量注册控件权限映射完成，成功: 116/116
2025-08-03 14:34:02 [DEBUG] HyExcel控件权限映射注册完成，共注册 116 个控件
2025-08-03 14:34:02 [INFO] 开始初始化权限验证
2025-08-03 14:34:02 [DEBUG] 设置默认UI可见性为false
2025-08-03 14:34:02 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 14:34:03 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 12329192
2025-08-03 14:34:03 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 12329192)
2025-08-03 14:34:03 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 14:34:03 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-03 14:34:03 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:34:03 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:03 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:34:03 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:03 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:34:03 [DEBUG] 开始重置 208 个命令栏
2025-08-03 14:34:03 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:34:03 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:03 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:34:03 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:03 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:34:03 [DEBUG] 开始检查所有需要的权限
2025-08-03 14:34:03 [INFO] 所有权限检查完成
2025-08-03 14:34:03 [DEBUG] 应用权限状态到UI控件
2025-08-03 14:34:03 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:34:03 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:03 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:34:03 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:03 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:34:03 [DEBUG] 启动后台权限刷新任务
2025-08-03 14:34:03 [DEBUG] 启动延迟权限刷新任务
2025-08-03 14:34:03 [INFO] 权限验证初始化完成
2025-08-03 14:34:03 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:34:03 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:03 [INFO] UI权限管理初始化完成
2025-08-03 14:34:03 [INFO] 收到权限管理器初始化完成通知
2025-08-03 14:34:03 [INFO] 开始刷新控件标题
2025-08-03 14:34:03 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:34:03 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 14:34:03 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:34:03 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 14:34:03 [DEBUG] 控件标题刷新完成
2025-08-03 14:34:03 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 14:34:03 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:03 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 14:34:03 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:34:03 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:34:03 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 14:34:03 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:03 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 14:34:03 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:03 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 14:34:03 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 14:34:03 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 14:34:03 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:34:03 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 14:34:03 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 14:34:03 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 14:34:03 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 14:34:03 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 14:34:03 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 14:34:03 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 14:34:03 [INFO] 控件标题更正完成
2025-08-03 14:34:03 [INFO] 控件标题刷新完成
2025-08-03 14:34:03 [INFO] 权限管理器初始化完成处理结束
2025-08-03 14:34:03 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-03 14:34:03 [DEBUG] 授权验证初始化完成
2025-08-03 14:34:03 [INFO] 授权状态刷新完成
2025-08-03 14:34:04 [DEBUG] 重置命令栏: cell
2025-08-03 14:34:04 [DEBUG] 重置命令栏: column
2025-08-03 14:34:04 [DEBUG] 重置命令栏: row
2025-08-03 14:34:04 [DEBUG] 重置命令栏: cell
2025-08-03 14:34:04 [DEBUG] 重置命令栏: column
2025-08-03 14:34:04 [DEBUG] 重置命令栏: row
2025-08-03 14:34:04 [DEBUG] 重置命令栏: row
2025-08-03 14:34:04 [DEBUG] 重置命令栏: column
2025-08-03 14:34:05 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-08-03 14:34:05 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:34:05 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:34:05 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:05 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:34:05 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:34:05 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:34:05 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:34:07 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 14:34:07 [DEBUG] 授权控制器已初始化
2025-08-03 14:34:07 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 14:34:08 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 14:34:08 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 14:34:08 [DEBUG] 授权控制器已初始化
2025-08-03 14:34:08 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 14:34:09 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 14:34:09 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 14:34:09 [DEBUG] 授权控制器已初始化
2025-08-03 14:34:09 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 14:34:10 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 14:34:10 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 14:34:10 [DEBUG] 授权控制器已初始化
2025-08-03 14:34:10 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 14:34:11 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 14:34:11 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 14:34:11 [DEBUG] 授权控制器已初始化
2025-08-03 14:34:11 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 14:34:12 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 14:34:12 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 14:34:12 [DEBUG] 授权控制器已初始化
2025-08-03 14:34:12 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 14:34:13 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 14:34:13 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 14:34:13 [DEBUG] 授权控制器已初始化
2025-08-03 14:34:13 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 14:34:14 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 14:34:15 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 14:34:15 [DEBUG] 授权控制器已初始化
2025-08-03 14:34:15 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 14:43:33 [INFO] 启动网络授权信息获取任务（后台更新）
2025-08-03 14:43:33 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-08-03 14:43:33 [INFO] [ET.ETForm] 已创建默认配置文件：D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\收藏文件.config
2025-08-03 14:43:33 [INFO] [ET.ETForm] 成功加载RibbonGallery配置文件菜单：收藏文件.config，共 3 个配置项
2025-08-03 14:43:33 [INFO] 从Remote成功获取到网络授权信息
2025-08-03 14:43:33 [INFO] 网络授权信息已更新并触发回调
2025-08-03 14:43:33 [INFO] 网络授权信息已从 Network 更新
2025-08-03 14:43:33 [INFO] 授权版本: 1.0
2025-08-03 14:43:33 [INFO] 颁发者: ExtensionsTools
2025-08-03 14:43:33 [INFO] 用户数量: 3
2025-08-03 14:43:33 [INFO] 分组权限数量: 2
2025-08-03 14:43:33 [WARN] 配置文件中未找到用户组信息
2025-08-03 14:43:33 [INFO] 已重新设置用户组: []
2025-08-03 14:43:33 [INFO] 用户组信息已重新设置
2025-08-03 14:43:33 [INFO] 立即刷新权限缓存和UI界面
2025-08-03 14:43:33 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-03 14:43:33 [DEBUG] 使用新的权限管理器进行强制刷新
2025-08-03 14:43:33 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-08-03 14:43:33 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-03 14:43:33 [DEBUG] 本地权限缓存已清空
2025-08-03 14:43:33 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-08-03 14:43:33 [INFO] 所有权限检查完成
2025-08-03 14:43:33 [DEBUG] 权限重新检查完成
2025-08-03 14:43:33 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:43:33 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:43:33 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:43:33 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:43:33 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:43:33 [INFO] UI界面权限状态已更新
2025-08-03 14:43:33 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 14:43:33 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:43:33 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:43:33 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 14:43:33 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-08-03 14:43:33 [INFO] 权限缓存和UI界面立即刷新完成
2025-08-03 14:43:33 [INFO] 网络授权已更新，开始刷新控件标题
2025-08-03 14:43:33 [INFO] 开始刷新Ribbon控件标题
2025-08-03 14:43:33 [DEBUG] 权限缓存已清空，清除了 116 个缓存项
2025-08-03 14:43:33 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-08-03 14:43:33 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 14:43:33 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 14:43:33 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:43:33 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 14:43:33 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:43:33 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:43:33 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:43:33 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 14:43:33 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 14:43:33 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 14:43:33 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 14:43:33 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 14:43:33 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 14:43:33 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 14:43:33 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:43:33 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 14:43:33 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 14:43:33 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 14:43:33 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 14:43:33 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 14:43:33 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 14:43:33 [INFO] 控件标题更正完成
2025-08-03 14:43:33 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-08-03 14:43:33 [INFO] Ribbon控件标题刷新完成
2025-08-03 14:43:33 [INFO] 控件标题刷新完成
2025-08-03 14:43:33 [DEBUG] Ribbon控件标题已刷新
2025-08-03 14:43:33 [INFO] 开始刷新控件标题
2025-08-03 14:43:33 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 14:43:33 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 14:43:33 [DEBUG] 控件标题刷新完成
2025-08-03 14:43:33 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 14:43:33 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 14:43:33 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 14:43:33 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:43:33 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 14:43:33 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 14:43:33 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 14:43:33 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 14:43:33 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 14:43:33 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 14:43:33 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 14:43:33 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:43:33 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 14:43:33 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 14:43:33 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 14:43:33 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 14:43:33 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 14:43:33 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 14:43:33 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 14:43:33 [INFO] 控件标题更正完成
2025-08-03 14:43:33 [INFO] 控件标题刷新完成
2025-08-03 14:43:33 [DEBUG] Ribbon控件标题已立即刷新
2025-08-03 14:43:34 [INFO] 开始刷新授权状态
2025-08-03 14:43:34 [DEBUG] 开始初始化授权验证
2025-08-03 14:43:34 [DEBUG] 使用新的权限管理器进行初始化
2025-08-03 14:43:34 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-03 14:43:34 [INFO] 开始初始化UI权限管理
2025-08-03 14:43:34 [DEBUG] [实例ID: 3cb7e4eb] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-03 14:43:34 [DEBUG] 🔍 [实例ID: 3cb7e4eb] 字典引用一致性检查:
2025-08-03 14:43:34 [DEBUG] 🔍   标题映射一致性: True
2025-08-03 14:43:34 [DEBUG] 🔍   权限映射一致性: True
2025-08-03 14:43:34 [DEBUG] 🔍   信息映射一致性: True
2025-08-03 14:43:34 [DEBUG] 🔍   特殊控件一致性: True
2025-08-03 14:43:34 [DEBUG] 控件权限管理器初始化完成 [实例ID: 3cb7e4eb]
2025-08-03 14:43:34 [DEBUG] 开始注册控件权限映射
2025-08-03 14:43:34 [DEBUG] 批量注册控件权限映射完成，成功: 116/116
2025-08-03 14:43:34 [DEBUG] HyExcel控件权限映射注册完成，共注册 116 个控件
2025-08-03 14:43:34 [INFO] 开始初始化权限验证
2025-08-03 14:43:34 [DEBUG] 设置默认UI可见性为false
2025-08-03 14:43:34 [DEBUG] 开始检查所有需要的权限
2025-08-03 14:43:34 [INFO] 所有权限检查完成
2025-08-03 14:43:34 [DEBUG] 应用权限状态到UI控件
2025-08-03 14:43:34 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:43:34 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:43:34 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:43:34 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:43:34 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:43:34 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:43:34 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:43:34 [DEBUG] 启动后台权限刷新任务
2025-08-03 14:43:34 [DEBUG] 启动延迟权限刷新任务
2025-08-03 14:43:34 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:43:34 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:43:34 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:43:34 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:43:34 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:43:34 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:43:34 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:43:34 [INFO] 权限验证初始化完成
2025-08-03 14:43:34 [INFO] UI权限管理初始化完成
2025-08-03 14:43:34 [INFO] 收到权限管理器初始化完成通知
2025-08-03 14:43:34 [INFO] 开始刷新控件标题
2025-08-03 14:43:34 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 14:43:34 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 14:43:34 [DEBUG] 控件标题刷新完成
2025-08-03 14:43:34 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 14:43:34 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 14:43:34 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 14:43:34 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:43:34 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 14:43:34 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 14:43:34 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 14:43:34 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 14:43:34 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 14:43:34 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 14:43:34 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 14:43:34 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 14:43:34 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 14:43:34 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 14:43:34 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 14:43:34 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 14:43:34 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 14:43:34 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 14:43:34 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 14:43:34 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 14:43:34 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 14:43:34 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 14:43:34 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 14:43:34 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 14:43:34 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 14:43:34 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 14:43:34 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 14:43:34 [INFO] 控件标题更正完成
2025-08-03 14:43:34 [INFO] 控件标题刷新完成
2025-08-03 14:43:34 [INFO] 权限管理器初始化完成处理结束
2025-08-03 14:43:34 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-03 14:43:34 [DEBUG] 授权验证初始化完成
2025-08-03 14:43:34 [INFO] 授权状态刷新完成
2025-08-03 14:43:36 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 14:43:36 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 14:43:36 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:43:36 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 14:43:36 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 14:43:36 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 14:43:36 [DEBUG] 已应用权限状态到UI控件
2025-08-03 14:43:36 [INFO] [ET.ETForm] 成功加载RibbonGallery配置文件菜单：收藏文件.config，共 3 个配置项
2025-08-03 14:43:43 [INFO] [ET.ETForm] 用户点击刷新菜单：收藏文件.config
2025-08-03 14:43:43 [INFO] [ET.ETForm] 成功加载RibbonGallery配置文件菜单：收藏文件.config，共 3 个配置项
2025-08-03 14:43:46 [INFO] [ET.ETForm] 成功加载RibbonGallery配置文件菜单：收藏文件.config，共 3 个配置项
2025-08-03 14:44:11 [INFO] [ET.ETForm] 成功加载RibbonGallery配置文件菜单：收藏文件.config，共 3 个配置项
2025-08-03 14:44:17 [INFO] [ET.ETForm] 成功加载RibbonGallery配置文件菜单：收藏文件.config，共 3 个配置项
2025-08-03 14:44:23 [INFO] [ET.ETForm] 用户点击刷新菜单：收藏文件.config
2025-08-03 14:44:23 [INFO] [ET.ETForm] 成功加载RibbonGallery配置文件菜单：收藏文件.config，共 3 个配置项
2025-08-03 16:10:09 [INFO] Excel窗口句柄监控器初始化完成
2025-08-03 16:10:09 [INFO] 配置文件实例已在加载时初始化
2025-08-03 16:10:09 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-08-03 16:10:09 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-08-03 16:10:09 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-08-03 16:10:09 [INFO] 成功初始化Excel应用程序实例
2025-08-03 16:10:10 [INFO] 自动备份路径未配置
2025-08-03 16:10:10 [DEBUG] 开始初始化授权控制器
2025-08-03 16:10:10 [DEBUG] 授权系统初始化完成，耗时: 555ms
2025-08-03 16:10:10 [DEBUG] 开始初始化授权验证
2025-08-03 16:10:10 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-08-03 16:10:10 [DEBUG] 权限管理器初始化成功
2025-08-03 16:10:10 [DEBUG] 使用新的权限管理器进行初始化
2025-08-03 16:10:10 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-03 16:10:10 [INFO] 开始初始化UI权限管理
2025-08-03 16:10:10 [DEBUG] [实例ID: 3624e73d] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-03 16:10:10 [DEBUG] 🔍 [实例ID: 3624e73d] 字典引用一致性检查:
2025-08-03 16:10:10 [DEBUG] 🔍   标题映射一致性: True
2025-08-03 16:10:10 [DEBUG] 🔍   权限映射一致性: True
2025-08-03 16:10:10 [DEBUG] 🔍   信息映射一致性: True
2025-08-03 16:10:10 [DEBUG] 🔍   特殊控件一致性: True
2025-08-03 16:10:10 [DEBUG] 控件权限管理器初始化完成 [实例ID: 3624e73d]
2025-08-03 16:10:10 [DEBUG] 开始注册控件权限映射
2025-08-03 16:10:10 [INFO] 开始初始化全局控件映射
2025-08-03 16:10:10 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-08-03 16:10:10 [DEBUG] 开始生成控件标题映射
2025-08-03 16:10:10 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-08-03 16:10:10 [DEBUG] 通过反射获取到 124 个字段
2025-08-03 16:10:10 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:10 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:10 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-08-03 16:10:10 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-08-03 16:10:10 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 16:10:10 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-08-03 16:10:10 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-08-03 16:10:10 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-08-03 16:10:10 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 16:10:10 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-08-03 16:10:10 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 16:10:10 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-08-03 16:10:10 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-08-03 16:10:10 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-08-03 16:10:10 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:10 [INFO] 控件结构获取完成，共获取到 122 个控件
2025-08-03 16:10:10 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-03 16:10:10 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-08-03 16:10:10 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-08-03 16:10:10 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-08-03 16:10:10 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-08-03 16:10:10 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-08-03 16:10:10 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-08-03 16:10:10 [INFO] 控件标题映射生成完成，共生成 109 项映射
2025-08-03 16:10:10 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-03 16:10:10 [DEBUG] 全局控件标题映射生成完成，共生成 109 项
2025-08-03 16:10:10 [INFO] 关键控件标题映射: hyTab -> Develop
2025-08-03 16:10:10 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-08-03 16:10:10 [WARN] 关键控件未找到标题映射: buttonAbout
2025-08-03 16:10:10 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-08-03 16:10:10 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-08-03 16:10:10 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-08-03 16:10:10 [INFO] === znAbout控件标题映射诊断 ===
2025-08-03 16:10:10 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-08-03 16:10:10 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-08-03 16:10:10 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-08-03 16:10:10 [DEBUG] === 所有生成的控件标题映射 ===
2025-08-03 16:10:10 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-08-03 16:10:10 [DEBUG] 控件映射: btnAngleExtractor -> '方向角/下倾角提取'
2025-08-03 16:10:10 [DEBUG] 控件映射: btnStationConverter -> '站点系统数量统计'
2025-08-03 16:10:10 [DEBUG] 控件映射: btnStationDataProcessor -> '基站台账数据转换处理'
2025-08-03 16:10:10 [DEBUG] 控件映射: btnTowerAccountProcessor -> '铁塔内部台账转换工具'
2025-08-03 16:10:10 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-08-03 16:10:10 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-08-03 16:10:10 [DEBUG] 控件映射: btn发送及存档 -> '临时/发送/存档'
2025-08-03 16:10:10 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-08-03 16:10:10 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-08-03 16:10:10 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-08-03 16:10:10 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-08-03 16:10:10 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-08-03 16:10:10 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规性检查'
2025-08-03 16:10:10 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-08-03 16:10:10 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-08-03 16:10:10 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-08-03 16:10:10 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-08-03 16:10:10 [DEBUG] 控件映射: button10 -> '记录当前文件'
2025-08-03 16:10:10 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-08-03 16:10:10 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-08-03 16:10:10 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-08-03 16:10:10 [DEBUG] 控件映射: button14 -> '临时/发送/存档'
2025-08-03 16:10:10 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-08-03 16:10:10 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-08-03 16:10:10 [DEBUG] 控件映射: button17 -> '向下填充'
2025-08-03 16:10:10 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-08-03 16:10:10 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-08-03 16:10:10 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-08-03 16:10:10 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-08-03 16:10:10 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-08-03 16:10:10 [DEBUG] 控件映射: button3 -> '关于'
2025-08-03 16:10:10 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-08-03 16:10:10 [DEBUG] 控件映射: button5 -> '最近打开文件'
2025-08-03 16:10:10 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-08-03 16:10:10 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-08-03 16:10:10 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-08-03 16:10:10 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-08-03 16:10:10 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-08-03 16:10:10 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-08-03 16:10:10 [DEBUG] 控件映射: button9 -> '最近打开文件'
2025-08-03 16:10:10 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-08-03 16:10:10 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-08-03 16:10:10 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-08-03 16:10:10 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-08-03 16:10:10 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-08-03 16:10:10 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-08-03 16:10:10 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-08-03 16:10:10 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-08-03 16:10:10 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-08-03 16:10:10 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-08-03 16:10:10 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-08-03 16:10:10 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-08-03 16:10:10 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-08-03 16:10:10 [DEBUG] 控件映射: button打开文件 -> '打开文件'
2025-08-03 16:10:10 [DEBUG] 控件映射: button多边形GPS坐标转换器 -> '多边形GPS坐标转换器'
2025-08-03 16:10:10 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-08-03 16:10:10 [DEBUG] 控件映射: button记录当前文件 -> '记录当前文件'
2025-08-03 16:10:10 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-08-03 16:10:10 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-08-03 16:10:10 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-08-03 16:10:10 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-08-03 16:10:10 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-08-03 16:10:10 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-08-03 16:10:10 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-08-03 16:10:10 [DEBUG] 控件映射: button铁塔KML点图转换 -> '铁塔KML点图转换'
2025-08-03 16:10:10 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-08-03 16:10:10 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-08-03 16:10:10 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-08-03 16:10:10 [DEBUG] 控件映射: button文件操作 -> '文件查找/复制/改名'
2025-08-03 16:10:10 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-08-03 16:10:10 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-08-03 16:10:10 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-08-03 16:10:10 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-08-03 16:10:10 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-08-03 16:10:10 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-08-03 16:10:10 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-08-03 16:10:10 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-08-03 16:10:10 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-08-03 16:10:10 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-08-03 16:10:10 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-08-03 16:10:10 [DEBUG] 控件映射: group1 -> '关于'
2025-08-03 16:10:10 [DEBUG] 控件映射: group2 -> '脚本'
2025-08-03 16:10:10 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-08-03 16:10:10 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-08-03 16:10:10 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-08-03 16:10:10 [DEBUG] 控件映射: group文件 -> '文件'
2025-08-03 16:10:10 [DEBUG] 控件映射: group无线 -> '无线'
2025-08-03 16:10:10 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-08-03 16:10:10 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-08-03 16:10:10 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-08-03 16:10:10 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-08-03 16:10:10 [DEBUG] 控件映射: menu1 -> '基站数据处理'
2025-08-03 16:10:10 [DEBUG] 控件映射: menu2 -> '其它'
2025-08-03 16:10:10 [DEBUG] 控件映射: menu3 -> '设置'
2025-08-03 16:10:10 [DEBUG] 控件映射: menu5 -> '修复'
2025-08-03 16:10:10 [DEBUG] 控件映射: menuHY -> '其它'
2025-08-03 16:10:10 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-08-03 16:10:10 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-08-03 16:10:10 [DEBUG] 控件映射: menu修复 -> '修复'
2025-08-03 16:10:10 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-08-03 16:10:10 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-08-03 16:10:10 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-08-03 16:10:10 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-08-03 16:10:10 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-08-03 16:10:10 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-08-03 16:10:10 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-08-03 16:10:10 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-08-03 16:10:10 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-08-03 16:10:11 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-08-03 16:10:11 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-08-03 16:10:11 [DEBUG] 开始生成控件权限映射
2025-08-03 16:10:11 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-08-03 16:10:11 [DEBUG] 通过反射获取到 124 个字段
2025-08-03 16:10:11 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:11 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:10:11 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-08-03 16:10:11 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-08-03 16:10:11 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 16:10:11 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-08-03 16:10:11 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-08-03 16:10:11 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-08-03 16:10:11 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 16:10:11 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-08-03 16:10:11 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 16:10:11 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-08-03 16:10:11 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-08-03 16:10:11 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-08-03 16:10:11 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:10:11 [INFO] 控件结构获取完成，共获取到 122 个控件
2025-08-03 16:10:11 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-03 16:10:11 [INFO] 控件权限映射生成完成，共生成 116 项映射
2025-08-03 16:10:11 [DEBUG] 全局控件权限映射生成完成，共生成 116 项
2025-08-03 16:10:11 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-08-03 16:10:11 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-08-03 16:10:11 [INFO] 全局控件映射初始化完成 - 标题映射: 109 项, 权限映射: 116 项
2025-08-03 16:10:11 [DEBUG] 批量注册控件权限映射完成，成功: 116/116
2025-08-03 16:10:11 [DEBUG] HyExcel控件权限映射注册完成，共注册 116 个控件
2025-08-03 16:10:11 [INFO] 开始初始化权限验证
2025-08-03 16:10:11 [DEBUG] 设置默认UI可见性为false
2025-08-03 16:10:11 [DEBUG] 开始检查所有需要的权限
2025-08-03 16:10:11 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-08-03 16:10:11 [INFO] 启动网络授权信息获取任务
2025-08-03 16:10:11 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-08-03 16:10:11 [INFO] 所有权限检查完成
2025-08-03 16:10:11 [DEBUG] 应用权限状态到UI控件
2025-08-03 16:10:11 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:10:11 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:10:11 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:10:11 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:10:11 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:10:11 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:10:11 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:10:11 [DEBUG] 启动后台权限刷新任务
2025-08-03 16:10:11 [DEBUG] 启动延迟权限刷新任务
2025-08-03 16:10:11 [INFO] 权限验证初始化完成
2025-08-03 16:10:11 [INFO] UI权限管理初始化完成
2025-08-03 16:10:11 [INFO] 收到权限管理器初始化完成通知
2025-08-03 16:10:11 [INFO] 开始刷新控件标题
2025-08-03 16:10:11 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 16:10:11 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 16:10:11 [DEBUG] 控件标题刷新完成
2025-08-03 16:10:11 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 16:10:11 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 16:10:11 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 16:10:11 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:10:11 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 16:10:11 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 16:10:11 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 16:10:11 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 16:10:11 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 16:10:11 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 16:10:11 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 16:10:11 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:10:11 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 16:10:11 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 16:10:11 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 16:10:11 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 16:10:11 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 16:10:11 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 16:10:11 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 16:10:11 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 16:10:11 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 16:10:11 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 16:10:11 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 16:10:11 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 16:10:11 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 16:10:11 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 16:10:11 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 16:10:11 [INFO] 控件标题更正完成
2025-08-03 16:10:11 [INFO] 控件标题刷新完成
2025-08-03 16:10:11 [INFO] 权限管理器初始化完成处理结束
2025-08-03 16:10:11 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-03 16:10:11 [DEBUG] 授权验证初始化完成
2025-08-03 16:10:11 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-08-03 16:10:11 [INFO] 成功加载配置和授权信息
2025-08-03 16:10:11 [INFO] 开始初始化定时器和设置
2025-08-03 16:10:11 [INFO] 定时器和设置初始化完成
2025-08-03 16:10:11 [INFO] 开始VSTO插件启动流程
2025-08-03 16:10:11 [INFO] TopMostForm窗体加载完成
2025-08-03 16:10:12 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:10:12 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16263484
2025-08-03 16:10:12 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16263484)
2025-08-03 16:10:12 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 16263484
2025-08-03 16:10:12 [INFO] 系统事件监控已启动
2025-08-03 16:10:12 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:10:12 [INFO] OpenForm: 窗体标题已设置为类名 'CrosshairOverlayForm'
2025-08-03 16:10:12 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-08-03 16:10:12 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-08-03 16:10:12 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-08-03 16:10:12 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 53745856
2025-08-03 16:10:12 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-08-03 16:10:12 [INFO] VSTO插件启动流程完成
2025-08-03 16:10:12 [INFO] 从Remote成功获取到网络授权信息
2025-08-03 16:10:12 [INFO] 网络授权信息已更新并触发回调
2025-08-03 16:10:12 [INFO] 网络授权信息已从 Network 更新
2025-08-03 16:10:12 [INFO] 授权版本: 1.0
2025-08-03 16:10:12 [INFO] 颁发者: ExtensionsTools
2025-08-03 16:10:12 [INFO] 用户数量: 3
2025-08-03 16:10:12 [INFO] 分组权限数量: 2
2025-08-03 16:10:12 [WARN] 配置文件中未找到用户组信息
2025-08-03 16:10:12 [INFO] 已重新设置用户组: []
2025-08-03 16:10:12 [INFO] 用户组信息已重新设置
2025-08-03 16:10:12 [INFO] 立即刷新权限缓存和UI界面
2025-08-03 16:10:12 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-03 16:10:12 [DEBUG] 使用新的权限管理器进行强制刷新
2025-08-03 16:10:12 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-08-03 16:10:12 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-03 16:10:12 [DEBUG] 本地权限缓存已清空
2025-08-03 16:10:12 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-08-03 16:10:12 [INFO] 所有权限检查完成
2025-08-03 16:10:12 [DEBUG] 权限重新检查完成
2025-08-03 16:10:12 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:10:12 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:10:12 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:10:12 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:10:12 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:10:12 [INFO] UI界面权限状态已更新
2025-08-03 16:10:12 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 16:10:12 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 16:10:12 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-08-03 16:10:12 [INFO] 权限缓存和UI界面立即刷新完成
2025-08-03 16:10:12 [INFO] 网络授权已更新，开始刷新控件标题
2025-08-03 16:10:12 [INFO] 开始刷新Ribbon控件标题
2025-08-03 16:10:12 [DEBUG] 权限缓存已清空，清除了 116 个缓存项
2025-08-03 16:10:12 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-08-03 16:10:12 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 16:10:12 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 16:10:12 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 16:10:12 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:10:12 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 16:10:12 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 16:10:12 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 16:10:12 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 16:10:12 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 16:10:12 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 16:10:12 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 16:10:12 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:10:12 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 16:10:12 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 16:10:12 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 16:10:12 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 16:10:12 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 16:10:12 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 16:10:12 [INFO] 控件标题更正完成
2025-08-03 16:10:12 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-08-03 16:10:12 [INFO] Ribbon控件标题刷新完成
2025-08-03 16:10:12 [INFO] 控件标题刷新完成
2025-08-03 16:10:12 [DEBUG] Ribbon控件标题已刷新
2025-08-03 16:10:12 [INFO] 开始刷新控件标题
2025-08-03 16:10:12 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 16:10:12 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 16:10:12 [DEBUG] 控件标题刷新完成
2025-08-03 16:10:12 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 16:10:12 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 16:10:12 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 16:10:12 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:10:12 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 16:10:12 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 16:10:12 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 16:10:12 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 16:10:12 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 16:10:12 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 16:10:12 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 16:10:12 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:10:12 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 16:10:12 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 16:10:12 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 16:10:12 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 16:10:12 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 16:10:12 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 16:10:12 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 16:10:12 [INFO] 控件标题更正完成
2025-08-03 16:10:12 [INFO] 控件标题刷新完成
2025-08-03 16:10:12 [DEBUG] Ribbon控件标题已立即刷新
2025-08-03 16:10:12 [INFO] 开始刷新授权状态
2025-08-03 16:10:12 [DEBUG] 开始初始化授权验证
2025-08-03 16:10:12 [DEBUG] 使用新的权限管理器进行初始化
2025-08-03 16:10:12 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-03 16:10:12 [INFO] 开始初始化UI权限管理
2025-08-03 16:10:12 [DEBUG] [实例ID: 01ec9037] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-03 16:10:12 [DEBUG] 🔍 [实例ID: 01ec9037] 字典引用一致性检查:
2025-08-03 16:10:12 [DEBUG] 🔍   标题映射一致性: True
2025-08-03 16:10:12 [DEBUG] 🔍   权限映射一致性: True
2025-08-03 16:10:12 [DEBUG] 🔍   信息映射一致性: True
2025-08-03 16:10:12 [DEBUG] 🔍   特殊控件一致性: True
2025-08-03 16:10:12 [DEBUG] 控件权限管理器初始化完成 [实例ID: 01ec9037]
2025-08-03 16:10:12 [DEBUG] 开始注册控件权限映射
2025-08-03 16:10:12 [DEBUG] 批量注册控件权限映射完成，成功: 116/116
2025-08-03 16:10:12 [DEBUG] HyExcel控件权限映射注册完成，共注册 116 个控件
2025-08-03 16:10:12 [INFO] 开始初始化权限验证
2025-08-03 16:10:12 [DEBUG] 设置默认UI可见性为false
2025-08-03 16:10:13 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:10:13 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 16263484
2025-08-03 16:10:13 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 16263484)
2025-08-03 16:10:13 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:10:13 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-03 16:10:13 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:10:13 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:10:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:10:13 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:10:13 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:10:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:10:13 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:10:13 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:10:13 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:10:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:10:13 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:10:13 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:10:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:10:13 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:10:13 [DEBUG] 开始重置 208 个命令栏
2025-08-03 16:10:13 [DEBUG] 开始检查所有需要的权限
2025-08-03 16:10:13 [INFO] 所有权限检查完成
2025-08-03 16:10:13 [DEBUG] 应用权限状态到UI控件
2025-08-03 16:10:13 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:10:13 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:10:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:10:13 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:10:13 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:10:13 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:10:13 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:10:13 [DEBUG] 启动后台权限刷新任务
2025-08-03 16:10:13 [DEBUG] 启动延迟权限刷新任务
2025-08-03 16:10:13 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:10:13 [INFO] 权限验证初始化完成
2025-08-03 16:10:13 [INFO] UI权限管理初始化完成
2025-08-03 16:10:13 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:10:14 [INFO] 收到权限管理器初始化完成通知
2025-08-03 16:10:14 [INFO] 开始刷新控件标题
2025-08-03 16:10:14 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 16:10:14 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:10:14 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 16:10:14 [DEBUG] 控件标题刷新完成
2025-08-03 16:10:14 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 16:10:14 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 16:10:14 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:10:14 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 16:10:14 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:10:14 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:10:14 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:10:14 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:10:14 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 16:10:14 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 16:10:14 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 16:10:14 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 16:10:14 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 16:10:14 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 16:10:14 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 16:10:14 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:10:14 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 16:10:14 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 16:10:14 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 16:10:14 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 16:10:14 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 16:10:14 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 16:10:14 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 16:10:14 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 16:10:14 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 16:10:14 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 16:10:14 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 16:10:14 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 16:10:14 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 16:10:14 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 16:10:14 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 16:10:14 [INFO] 控件标题更正完成
2025-08-03 16:10:14 [INFO] 控件标题刷新完成
2025-08-03 16:10:14 [INFO] 权限管理器初始化完成处理结束
2025-08-03 16:10:14 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-03 16:10:14 [DEBUG] 授权验证初始化完成
2025-08-03 16:10:14 [INFO] 授权状态刷新完成
2025-08-03 16:10:14 [DEBUG] 重置命令栏: cell
2025-08-03 16:10:14 [DEBUG] 重置命令栏: column
2025-08-03 16:10:14 [DEBUG] 重置命令栏: row
2025-08-03 16:10:14 [DEBUG] 重置命令栏: cell
2025-08-03 16:10:14 [DEBUG] 重置命令栏: column
2025-08-03 16:10:14 [DEBUG] 重置命令栏: row
2025-08-03 16:10:14 [DEBUG] 重置命令栏: row
2025-08-03 16:10:14 [DEBUG] 重置命令栏: column
2025-08-03 16:10:15 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-08-03 16:10:15 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:10:15 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:10:15 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:10:15 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:10:15 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:10:15 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:10:15 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:10:16 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:10:16 [DEBUG] 授权控制器已初始化
2025-08-03 16:10:16 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:10:17 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:10:17 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:10:17 [DEBUG] 授权控制器已初始化
2025-08-03 16:10:17 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:10:18 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:10:18 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:10:18 [DEBUG] 授权控制器已初始化
2025-08-03 16:10:18 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:10:19 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:10:19 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:10:19 [DEBUG] 授权控制器已初始化
2025-08-03 16:10:19 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:10:21 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:10:21 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:10:21 [DEBUG] 授权控制器已初始化
2025-08-03 16:10:21 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:10:22 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:10:22 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:10:22 [DEBUG] 授权控制器已初始化
2025-08-03 16:10:22 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:10:23 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:10:23 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:10:23 [DEBUG] 授权控制器已初始化
2025-08-03 16:10:23 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:10:24 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:10:24 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:10:24 [DEBUG] 授权控制器已初始化
2025-08-03 16:10:24 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:10:24 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-08-03 16:10:24 [INFO] 系统事件监控已停止
2025-08-03 16:10:24 [INFO] Excel窗口句柄监控已停止
2025-08-03 16:10:24 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-08-03 16:10:25 [INFO] 开始VSTO插件关闭流程
2025-08-03 16:10:25 [INFO] 程序集追踪日志已保存到: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\logs\AssemblyTrace_20250803_161025.txt
2025-08-03 16:10:25 [INFO] VSTO插件关闭流程完成
2025-08-03 16:10:25 [ERROR] 菜单系统初始化失败
异常详情：System.Threading.ThreadAbortException: 正在中止线程。
   在 Microsoft.Office.Core.CommandBarControls.get_Count()
   在 HyExcelVsto.Extensions.MenuManager.AddMenuButton(CommandBarControls& controls, String menuCaption, String group, Boolean addClick, _CommandBarButtonEvents_ClickEventHandler clickEventHandler, Int32 index, Boolean beginGroup, Boolean visible) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\MenuManager.cs:行号 432
   在 HyExcelVsto.Extensions.MenuManager.RebuildWorksheetContextMenu(CommandBar bar, Boolean isCellContext, Boolean addClick) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\MenuManager.cs:行号 343
   在 HyExcelVsto.Extensions.MenuManager.UpdateWorksheetContextMenus(CommandBars bars) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\MenuManager.cs:行号 222
   在 HyExcelVsto.Extensions.MenuManager.RebuildAllMenus() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\MenuManager.cs:行号 49
   在 HyExcelVsto.ThisAddIn.EnsureMenuInitialized() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ThisAddIn.cs:行号 328
2025-08-03 16:10:25 [ERROR] 菜单系统启动时初始化失败
异常详情：System.Threading.ThreadAbortException: 正在中止线程。
   在 Microsoft.Office.Core.CommandBarControls.get_Count()
   在 HyExcelVsto.Extensions.MenuManager.AddMenuButton(CommandBarControls& controls, String menuCaption, String group, Boolean addClick, _CommandBarButtonEvents_ClickEventHandler clickEventHandler, Int32 index, Boolean beginGroup, Boolean visible) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\MenuManager.cs:行号 432
   在 HyExcelVsto.Extensions.MenuManager.RebuildWorksheetContextMenu(CommandBar bar, Boolean isCellContext, Boolean addClick) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\MenuManager.cs:行号 343
   在 HyExcelVsto.Extensions.MenuManager.UpdateWorksheetContextMenus(CommandBars bars) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\MenuManager.cs:行号 222
   在 HyExcelVsto.Extensions.MenuManager.RebuildAllMenus() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Extensions\MenuManager.cs:行号 49
   在 HyExcelVsto.ThisAddIn.EnsureMenuInitialized() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ThisAddIn.cs:行号 336
   在 HyExcelVsto.ThisAddIn.<ThisAddIn_Startup>b__45_1() 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ThisAddIn.cs:行号 273
2025-08-03 16:11:30 [INFO] App_WorkbookOpen: 工作簿 '设计内部台账20250801V1.xlsx' 打开事件触发
2025-08-03 16:11:30 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:11:30 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 7742166, 新父窗口: 37687456
2025-08-03 16:11:30 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 37687456)
2025-08-03 16:11:30 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:11:30 [INFO] App_WorkbookOpen: 工作簿 '设计内部台账20250801V1.xlsx' 打开处理完成
2025-08-03 16:11:30 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:11:30 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 37687456)
2025-08-03 16:11:30 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:11:30 [INFO] App_WorkbookActivate: 工作簿 '设计内部台账20250801V1.xlsx' 激活处理完成
2025-08-03 16:11:30 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:11:30 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 37687456)
2025-08-03 16:11:30 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:11:30 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 16:11:31 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:11:31 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:11:31 [WARN] 检测到Excel窗口句柄变化: 7742166 -> 37687456
2025-08-03 16:11:31 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 37687456
2025-08-03 16:11:31 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 37687456)
2025-08-03 16:11:31 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:11:31 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 37687456)
2025-08-03 16:11:31 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 37687456
2025-08-03 16:11:31 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 37687456)
2025-08-03 16:11:31 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:11:31 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:11:31 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 16:11:31 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 37687456)
2025-08-03 16:11:31 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:11:31 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-08-03 16:11:32 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-08-03 16:11:32 [INFO] 系统事件监控已停止
2025-08-03 16:11:32 [INFO] Excel窗口句柄监控已停止
2025-08-03 16:11:32 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-08-03 16:11:33 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:11:33 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 16:11:33 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 16:11:33 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 7742166
2025-08-03 16:11:33 [INFO] 系统事件监控已启动
2025-08-03 16:11:33 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:11:33 [INFO] App_WorkbookActivate: 工作簿 '一点一案上传方案信息表-20250801-170610.xlsx' 激活处理完成
2025-08-03 16:11:33 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:11:33 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-03 16:11:33 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:11:33 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 16:11:33 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:11:33 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:11:33 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 7742166
2025-08-03 16:11:33 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 7742166)
2025-08-03 16:11:33 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:11:33 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 16:11:33 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 7742166)
2025-08-03 16:11:33 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:11:34 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-08-03 16:11:34 [INFO] 系统事件监控已停止
2025-08-03 16:11:34 [INFO] Excel窗口句柄监控已停止
2025-08-03 16:11:34 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-08-03 16:11:36 [INFO] 开始VSTO插件关闭流程
2025-08-03 16:11:36 [INFO] 程序集追踪日志已保存到: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\logs\AssemblyTrace_20250803_161136.txt
2025-08-03 16:11:36 [INFO] VSTO插件关闭流程完成
2025-08-03 16:15:49 [INFO] Excel窗口句柄监控器初始化完成
2025-08-03 16:15:49 [INFO] 配置文件实例已在加载时初始化
2025-08-03 16:15:49 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-08-03 16:15:49 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-08-03 16:15:49 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-08-03 16:15:49 [INFO] 成功初始化Excel应用程序实例
2025-08-03 16:15:49 [INFO] 自动备份路径未配置
2025-08-03 16:15:49 [DEBUG] 开始初始化授权控制器
2025-08-03 16:15:50 [DEBUG] 授权系统初始化完成，耗时: 322ms
2025-08-03 16:15:50 [DEBUG] 开始初始化授权验证
2025-08-03 16:15:50 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-08-03 16:15:50 [DEBUG] 权限管理器初始化成功
2025-08-03 16:15:50 [DEBUG] 使用新的权限管理器进行初始化
2025-08-03 16:15:50 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-03 16:15:50 [INFO] 开始初始化UI权限管理
2025-08-03 16:15:50 [DEBUG] [实例ID: a780adba] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-03 16:15:50 [DEBUG] 🔍 [实例ID: a780adba] 字典引用一致性检查:
2025-08-03 16:15:50 [DEBUG] 🔍   标题映射一致性: True
2025-08-03 16:15:50 [DEBUG] 🔍   权限映射一致性: True
2025-08-03 16:15:50 [DEBUG] 🔍   信息映射一致性: True
2025-08-03 16:15:50 [DEBUG] 🔍   特殊控件一致性: True
2025-08-03 16:15:50 [DEBUG] 控件权限管理器初始化完成 [实例ID: a780adba]
2025-08-03 16:15:50 [DEBUG] 开始注册控件权限映射
2025-08-03 16:15:50 [INFO] 开始初始化全局控件映射
2025-08-03 16:15:50 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-08-03 16:15:50 [DEBUG] 开始生成控件标题映射
2025-08-03 16:15:50 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-08-03 16:15:50 [DEBUG] 通过反射获取到 124 个字段
2025-08-03 16:15:50 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-08-03 16:15:50 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-08-03 16:15:50 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-08-03 16:15:50 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-08-03 16:15:50 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-08-03 16:15:50 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [INFO] 控件结构获取完成，共获取到 122 个控件
2025-08-03 16:15:50 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-03 16:15:50 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-08-03 16:15:50 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-08-03 16:15:50 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-08-03 16:15:50 [INFO] 控件标题映射生成完成，共生成 109 项映射
2025-08-03 16:15:50 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-03 16:15:50 [DEBUG] 全局控件标题映射生成完成，共生成 109 项
2025-08-03 16:15:50 [INFO] 关键控件标题映射: hyTab -> Develop
2025-08-03 16:15:50 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-08-03 16:15:50 [WARN] 关键控件未找到标题映射: buttonAbout
2025-08-03 16:15:50 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-08-03 16:15:50 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-08-03 16:15:50 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-08-03 16:15:50 [INFO] === znAbout控件标题映射诊断 ===
2025-08-03 16:15:50 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-08-03 16:15:50 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-08-03 16:15:50 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-08-03 16:15:50 [DEBUG] === 所有生成的控件标题映射 ===
2025-08-03 16:15:50 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-08-03 16:15:50 [DEBUG] 控件映射: btnAngleExtractor -> '方向角/下倾角提取'
2025-08-03 16:15:50 [DEBUG] 控件映射: btnStationConverter -> '站点系统数量统计'
2025-08-03 16:15:50 [DEBUG] 控件映射: btnStationDataProcessor -> '基站台账数据转换处理'
2025-08-03 16:15:50 [DEBUG] 控件映射: btnTowerAccountProcessor -> '铁塔内部台账转换工具'
2025-08-03 16:15:50 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-08-03 16:15:50 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-08-03 16:15:50 [DEBUG] 控件映射: btn发送及存档 -> '临时/发送/存档'
2025-08-03 16:15:50 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-08-03 16:15:50 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-08-03 16:15:50 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-08-03 16:15:50 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-08-03 16:15:50 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-08-03 16:15:50 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规性检查'
2025-08-03 16:15:50 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-08-03 16:15:50 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-08-03 16:15:50 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-08-03 16:15:50 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-08-03 16:15:50 [DEBUG] 控件映射: button10 -> '记录当前文件'
2025-08-03 16:15:50 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-08-03 16:15:50 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-08-03 16:15:50 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-08-03 16:15:50 [DEBUG] 控件映射: button14 -> '临时/发送/存档'
2025-08-03 16:15:50 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-08-03 16:15:50 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-08-03 16:15:50 [DEBUG] 控件映射: button17 -> '向下填充'
2025-08-03 16:15:50 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-08-03 16:15:50 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-08-03 16:15:50 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-08-03 16:15:50 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-08-03 16:15:50 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-08-03 16:15:50 [DEBUG] 控件映射: button3 -> '关于'
2025-08-03 16:15:50 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-08-03 16:15:50 [DEBUG] 控件映射: button5 -> '最近打开文件'
2025-08-03 16:15:50 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-08-03 16:15:50 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-08-03 16:15:50 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-08-03 16:15:50 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-08-03 16:15:50 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-08-03 16:15:50 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-08-03 16:15:50 [DEBUG] 控件映射: button9 -> '最近打开文件'
2025-08-03 16:15:50 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-08-03 16:15:50 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-08-03 16:15:50 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-08-03 16:15:50 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-08-03 16:15:50 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-08-03 16:15:50 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-08-03 16:15:50 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-08-03 16:15:50 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-08-03 16:15:50 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-08-03 16:15:50 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-08-03 16:15:50 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-08-03 16:15:50 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-08-03 16:15:50 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-08-03 16:15:50 [DEBUG] 控件映射: button打开文件 -> '打开文件'
2025-08-03 16:15:50 [DEBUG] 控件映射: button多边形GPS坐标转换器 -> '多边形GPS坐标转换器'
2025-08-03 16:15:50 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-08-03 16:15:50 [DEBUG] 控件映射: button记录当前文件 -> '记录当前文件'
2025-08-03 16:15:50 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-08-03 16:15:50 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-08-03 16:15:50 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-08-03 16:15:50 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-08-03 16:15:50 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-08-03 16:15:50 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-08-03 16:15:50 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-08-03 16:15:50 [DEBUG] 控件映射: button铁塔KML点图转换 -> '铁塔KML点图转换'
2025-08-03 16:15:50 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-08-03 16:15:50 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-08-03 16:15:50 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-08-03 16:15:50 [DEBUG] 控件映射: button文件操作 -> '文件查找/复制/改名'
2025-08-03 16:15:50 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-08-03 16:15:50 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-08-03 16:15:50 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-08-03 16:15:50 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-08-03 16:15:50 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-08-03 16:15:50 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-08-03 16:15:50 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-08-03 16:15:50 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-08-03 16:15:50 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-08-03 16:15:50 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-08-03 16:15:50 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-08-03 16:15:50 [DEBUG] 控件映射: group1 -> '关于'
2025-08-03 16:15:50 [DEBUG] 控件映射: group2 -> '脚本'
2025-08-03 16:15:50 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-08-03 16:15:50 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-08-03 16:15:50 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-08-03 16:15:50 [DEBUG] 控件映射: group文件 -> '文件'
2025-08-03 16:15:50 [DEBUG] 控件映射: group无线 -> '无线'
2025-08-03 16:15:50 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-08-03 16:15:50 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-08-03 16:15:50 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-08-03 16:15:50 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-08-03 16:15:50 [DEBUG] 控件映射: menu1 -> '基站数据处理'
2025-08-03 16:15:50 [DEBUG] 控件映射: menu2 -> '其它'
2025-08-03 16:15:50 [DEBUG] 控件映射: menu3 -> '设置'
2025-08-03 16:15:50 [DEBUG] 控件映射: menu5 -> '修复'
2025-08-03 16:15:50 [DEBUG] 控件映射: menuHY -> '其它'
2025-08-03 16:15:50 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-08-03 16:15:50 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-08-03 16:15:50 [DEBUG] 控件映射: menu修复 -> '修复'
2025-08-03 16:15:50 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-08-03 16:15:50 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-08-03 16:15:50 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-08-03 16:15:50 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-08-03 16:15:50 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-08-03 16:15:50 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-08-03 16:15:50 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-08-03 16:15:50 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-08-03 16:15:50 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-08-03 16:15:50 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-08-03 16:15:50 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-08-03 16:15:50 [DEBUG] 开始生成控件权限映射
2025-08-03 16:15:50 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-08-03 16:15:50 [DEBUG] 通过反射获取到 124 个字段
2025-08-03 16:15:50 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-08-03 16:15:50 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-08-03 16:15:50 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-08-03 16:15:50 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-08-03 16:15:50 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-08-03 16:15:50 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-08-03 16:15:50 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:15:50 [INFO] 控件结构获取完成，共获取到 122 个控件
2025-08-03 16:15:50 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-03 16:15:50 [INFO] 控件权限映射生成完成，共生成 116 项映射
2025-08-03 16:15:50 [DEBUG] 全局控件权限映射生成完成，共生成 116 项
2025-08-03 16:15:50 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-08-03 16:15:50 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-08-03 16:15:50 [INFO] 全局控件映射初始化完成 - 标题映射: 109 项, 权限映射: 116 项
2025-08-03 16:15:50 [DEBUG] 批量注册控件权限映射完成，成功: 116/116
2025-08-03 16:15:50 [DEBUG] HyExcel控件权限映射注册完成，共注册 116 个控件
2025-08-03 16:15:50 [INFO] 开始初始化权限验证
2025-08-03 16:15:50 [DEBUG] 设置默认UI可见性为false
2025-08-03 16:15:50 [DEBUG] 开始检查所有需要的权限
2025-08-03 16:15:50 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-08-03 16:15:50 [INFO] 启动网络授权信息获取任务
2025-08-03 16:15:50 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-08-03 16:15:50 [INFO] 所有权限检查完成
2025-08-03 16:15:50 [DEBUG] 应用权限状态到UI控件
2025-08-03 16:15:50 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:15:50 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:15:50 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:15:50 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:15:50 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:15:50 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:15:50 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:15:50 [DEBUG] 启动后台权限刷新任务
2025-08-03 16:15:50 [DEBUG] 启动延迟权限刷新任务
2025-08-03 16:15:50 [INFO] 权限验证初始化完成
2025-08-03 16:15:50 [INFO] UI权限管理初始化完成
2025-08-03 16:15:50 [INFO] 收到权限管理器初始化完成通知
2025-08-03 16:15:50 [INFO] 开始刷新控件标题
2025-08-03 16:15:50 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 16:15:50 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 16:15:50 [DEBUG] 控件标题刷新完成
2025-08-03 16:15:50 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 16:15:50 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 16:15:50 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 16:15:50 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:15:50 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 16:15:50 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 16:15:50 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 16:15:50 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 16:15:50 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 16:15:50 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 16:15:50 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 16:15:50 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:15:50 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 16:15:50 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 16:15:50 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 16:15:50 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 16:15:50 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 16:15:50 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 16:15:50 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 16:15:50 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 16:15:50 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 16:15:50 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 16:15:50 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 16:15:50 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 16:15:50 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 16:15:50 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 16:15:50 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 16:15:50 [INFO] 控件标题更正完成
2025-08-03 16:15:50 [INFO] 控件标题刷新完成
2025-08-03 16:15:50 [INFO] 权限管理器初始化完成处理结束
2025-08-03 16:15:50 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-03 16:15:50 [DEBUG] 授权验证初始化完成
2025-08-03 16:15:50 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-08-03 16:15:50 [INFO] 成功加载配置和授权信息
2025-08-03 16:15:50 [INFO] 开始初始化定时器和设置
2025-08-03 16:15:50 [INFO] 定时器和设置初始化完成
2025-08-03 16:15:50 [INFO] 开始VSTO插件启动流程
2025-08-03 16:15:50 [INFO] TopMostForm窗体加载完成
2025-08-03 16:15:50 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:15:50 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 24839490
2025-08-03 16:15:50 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 24839490)
2025-08-03 16:15:50 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 24839490
2025-08-03 16:15:50 [INFO] 系统事件监控已启动
2025-08-03 16:15:50 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:15:50 [INFO] OpenForm: 窗体标题已设置为类名 'CrosshairOverlayForm'
2025-08-03 16:15:50 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-08-03 16:15:50 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-08-03 16:15:50 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-08-03 16:15:50 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 5776936
2025-08-03 16:15:50 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-08-03 16:15:50 [INFO] VSTO插件启动流程完成
2025-08-03 16:15:51 [INFO] 从Remote成功获取到网络授权信息
2025-08-03 16:15:51 [INFO] 网络授权信息已更新并触发回调
2025-08-03 16:15:51 [INFO] 网络授权信息已从 Network 更新
2025-08-03 16:15:51 [INFO] 授权版本: 1.0
2025-08-03 16:15:51 [INFO] 颁发者: ExtensionsTools
2025-08-03 16:15:51 [INFO] 用户数量: 3
2025-08-03 16:15:51 [INFO] 分组权限数量: 2
2025-08-03 16:15:51 [WARN] 配置文件中未找到用户组信息
2025-08-03 16:15:51 [INFO] 已重新设置用户组: []
2025-08-03 16:15:51 [INFO] 用户组信息已重新设置
2025-08-03 16:15:51 [INFO] 立即刷新权限缓存和UI界面
2025-08-03 16:15:51 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-03 16:15:51 [DEBUG] 使用新的权限管理器进行强制刷新
2025-08-03 16:15:51 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-08-03 16:15:51 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-03 16:15:51 [DEBUG] 本地权限缓存已清空
2025-08-03 16:15:51 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-08-03 16:15:51 [INFO] 所有权限检查完成
2025-08-03 16:15:51 [DEBUG] 权限重新检查完成
2025-08-03 16:15:51 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:15:51 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:15:51 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:15:51 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:15:51 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:15:51 [INFO] UI界面权限状态已更新
2025-08-03 16:15:51 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 16:15:51 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 16:15:51 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-08-03 16:15:51 [INFO] 权限缓存和UI界面立即刷新完成
2025-08-03 16:15:51 [INFO] 网络授权已更新，开始刷新控件标题
2025-08-03 16:15:51 [INFO] 开始刷新Ribbon控件标题
2025-08-03 16:15:51 [DEBUG] 权限缓存已清空，清除了 116 个缓存项
2025-08-03 16:15:51 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-08-03 16:15:51 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 16:15:51 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 16:15:51 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 16:15:51 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:15:51 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 16:15:51 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 16:15:51 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 16:15:51 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 16:15:51 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 16:15:51 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 16:15:51 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 16:15:51 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:15:51 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 16:15:51 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 16:15:51 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 16:15:51 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 16:15:51 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 16:15:51 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 16:15:51 [INFO] 控件标题更正完成
2025-08-03 16:15:51 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-08-03 16:15:51 [INFO] Ribbon控件标题刷新完成
2025-08-03 16:15:51 [INFO] 控件标题刷新完成
2025-08-03 16:15:51 [DEBUG] Ribbon控件标题已刷新
2025-08-03 16:15:51 [INFO] 开始刷新控件标题
2025-08-03 16:15:51 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 16:15:51 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 16:15:51 [DEBUG] 控件标题刷新完成
2025-08-03 16:15:51 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 16:15:51 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 16:15:51 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 16:15:51 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:15:51 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 16:15:51 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 16:15:51 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 16:15:51 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 16:15:51 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 16:15:51 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 16:15:51 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 16:15:51 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:15:51 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 16:15:51 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 16:15:51 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 16:15:51 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 16:15:51 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 16:15:51 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 16:15:51 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 16:15:51 [INFO] 控件标题更正完成
2025-08-03 16:15:51 [INFO] 控件标题刷新完成
2025-08-03 16:15:51 [DEBUG] Ribbon控件标题已立即刷新
2025-08-03 16:15:51 [INFO] 开始刷新授权状态
2025-08-03 16:15:51 [DEBUG] 开始初始化授权验证
2025-08-03 16:15:51 [DEBUG] 使用新的权限管理器进行初始化
2025-08-03 16:15:51 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-03 16:15:51 [INFO] 开始初始化UI权限管理
2025-08-03 16:15:51 [DEBUG] [实例ID: 82dcbf33] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-03 16:15:51 [DEBUG] 🔍 [实例ID: 82dcbf33] 字典引用一致性检查:
2025-08-03 16:15:51 [DEBUG] 🔍   标题映射一致性: True
2025-08-03 16:15:51 [DEBUG] 🔍   权限映射一致性: True
2025-08-03 16:15:51 [DEBUG] 🔍   信息映射一致性: True
2025-08-03 16:15:51 [DEBUG] 🔍   特殊控件一致性: True
2025-08-03 16:15:51 [DEBUG] 控件权限管理器初始化完成 [实例ID: 82dcbf33]
2025-08-03 16:15:51 [DEBUG] 开始注册控件权限映射
2025-08-03 16:15:51 [DEBUG] 批量注册控件权限映射完成，成功: 116/116
2025-08-03 16:15:51 [DEBUG] HyExcel控件权限映射注册完成，共注册 116 个控件
2025-08-03 16:15:51 [INFO] 开始初始化权限验证
2025-08-03 16:15:51 [DEBUG] 设置默认UI可见性为false
2025-08-03 16:15:52 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:15:52 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 24839490
2025-08-03 16:15:52 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 24839490)
2025-08-03 16:15:52 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:15:52 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-03 16:15:52 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:15:52 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:15:52 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:15:52 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:15:52 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:15:52 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:15:52 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:15:52 [DEBUG] 开始重置 208 个命令栏
2025-08-03 16:15:52 [DEBUG] 开始检查所有需要的权限
2025-08-03 16:15:52 [INFO] 所有权限检查完成
2025-08-03 16:15:52 [DEBUG] 应用权限状态到UI控件
2025-08-03 16:15:52 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:15:52 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:15:52 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:15:52 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:15:52 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:15:52 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:15:52 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:15:52 [DEBUG] 启动后台权限刷新任务
2025-08-03 16:15:52 [DEBUG] 启动延迟权限刷新任务
2025-08-03 16:15:52 [INFO] 权限验证初始化完成
2025-08-03 16:15:52 [INFO] UI权限管理初始化完成
2025-08-03 16:15:52 [INFO] 收到权限管理器初始化完成通知
2025-08-03 16:15:52 [INFO] 开始刷新控件标题
2025-08-03 16:15:52 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 16:15:52 [DEBUG] 控件权限状态刷新完成，已检查 116 个控件
2025-08-03 16:15:52 [DEBUG] 控件标题刷新完成
2025-08-03 16:15:52 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 16:15:52 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 16:15:52 [DEBUG] 🔍 HyRibbon反射获取到 124 个字段
2025-08-03 16:15:52 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:15:52 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 16:15:52 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 16:15:52 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 16:15:52 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 16:15:52 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 16:15:52 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 16:15:52 [INFO] 动态获取到 122 个Ribbon控件引用
2025-08-03 16:15:52 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:15:52 [INFO] 开始批量更新控件标题，共 122 个控件
2025-08-03 16:15:52 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 16:15:52 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 16:15:52 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 16:15:52 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 16:15:52 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 16:15:52 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 16:15:52 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 16:15:52 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 16:15:52 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 16:15:52 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 16:15:52 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 16:15:52 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 16:15:52 [INFO] 批量更新控件标题完成，成功更新 109 个控件
2025-08-03 16:15:52 [INFO] 动态批量更新完成，共更新 122 个控件
2025-08-03 16:15:52 [INFO] 控件标题更正完成
2025-08-03 16:15:52 [INFO] 控件标题刷新完成
2025-08-03 16:15:52 [INFO] 权限管理器初始化完成处理结束
2025-08-03 16:15:52 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-03 16:15:52 [DEBUG] 授权验证初始化完成
2025-08-03 16:15:52 [INFO] 授权状态刷新完成
2025-08-03 16:15:52 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:15:52 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:15:52 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:15:52 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:15:52 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:15:52 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:15:52 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:15:52 [DEBUG] 重置命令栏: cell
2025-08-03 16:15:52 [DEBUG] 重置命令栏: column
2025-08-03 16:15:52 [DEBUG] 重置命令栏: row
2025-08-03 16:15:52 [DEBUG] 重置命令栏: cell
2025-08-03 16:15:52 [DEBUG] 重置命令栏: column
2025-08-03 16:15:52 [DEBUG] 重置命令栏: row
2025-08-03 16:15:52 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:15:52 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:15:52 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:15:52 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:15:52 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:15:52 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:15:52 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:15:52 [DEBUG] 重置命令栏: row
2025-08-03 16:15:52 [DEBUG] 重置命令栏: column
2025-08-03 16:15:53 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-08-03 16:15:54 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:15:54 [DEBUG] 授权控制器已初始化
2025-08-03 16:15:54 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:15:54 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:15:54 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:15:54 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:15:54 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:15:54 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:15:54 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:15:54 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:15:55 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:15:55 [INFO] [ET.ETForm] 成功加载RibbonGallery配置文件菜单：收藏文件.config，共 12 个配置项
2025-08-03 16:15:55 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:15:55 [DEBUG] 授权控制器已初始化
2025-08-03 16:15:55 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:15:56 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:15:56 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:15:56 [DEBUG] 授权控制器已初始化
2025-08-03 16:15:56 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:15:57 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:15:58 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:15:58 [DEBUG] 授权控制器已初始化
2025-08-03 16:15:58 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:15:58 [INFO] [ET.ETForm] 成功加载RibbonGallery配置文件菜单：收藏文件.config，共 12 个配置项
2025-08-03 16:15:58 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:15:59 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:15:59 [DEBUG] 授权控制器已初始化
2025-08-03 16:15:59 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:15:59 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:16:01 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:16:01 [DEBUG] 授权控制器已初始化
2025-08-03 16:16:01 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:16:03 [INFO] 用户取消了文件选择
2025-08-03 16:16:03 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:16:04 [INFO] OpenForm: 保持窗体现有标题 'Excel文件记录管理（双击文件名打开文件）'
2025-08-03 16:16:04 [INFO] OpenForm: 准备打开窗体 'Excel文件记录管理（双击文件名打开文件）'，位置: Center，单实例: True
2025-08-03 16:16:04 [INFO] 开始显示窗体 'Excel文件记录管理（双击文件名打开文件）'，位置模式: Center
2025-08-03 16:16:04 [INFO] 窗体 'Excel文件记录管理（双击文件名打开文件）' 以TopMostForm为父窗体显示
2025-08-03 16:16:04 [INFO] 窗体 'Excel文件记录管理（双击文件名打开文件）' 显示完成，句柄: 25041320
2025-08-03 16:16:04 [INFO] OpenForm: 窗体 'Excel文件记录管理（双击文件名打开文件）' 打开成功
2025-08-03 16:16:05 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:16:05 [DEBUG] 授权控制器已初始化
2025-08-03 16:16:05 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:16:05 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:16:05 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:16:05 [DEBUG] 授权控制器已初始化
2025-08-03 16:16:05 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:16:06 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:16:07 [DEBUG] 已重置工作表标签菜单
2025-08-03 16:16:07 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:16:12 [INFO] OpenForm: 保持窗体现有标题 '存档及发送'
2025-08-03 16:16:12 [INFO] OpenForm: 准备打开窗体 '存档及发送'，位置: Center，单实例: True
2025-08-03 16:16:12 [INFO] 开始显示窗体 '存档及发送'，位置模式: Center
2025-08-03 16:16:12 [INFO] [ET.ETFile] 成功使用 Unicode (UTF-8) 编码读取文件: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\Data\ET\Cache\ETForm\ComboBoxHistory\frm备份及发送_comboBox接收者.data
2025-08-03 16:16:12 [INFO] [ET.ETFile] 成功使用 Unicode (UTF-8) 编码读取文件: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.data\FileReceiver.txt
2025-08-03 16:16:12 [INFO] 文件接收者配置编辑器初始化完成，配置文件路径：D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.data\FileReceiver.txt
2025-08-03 16:16:12 [INFO] 窗体 '存档及发送' 以TopMostForm为父窗体显示
2025-08-03 16:16:12 [INFO] 窗体 '存档及发送' 显示完成，句柄: 40701720
2025-08-03 16:16:12 [INFO] OpenForm: 窗体 '存档及发送' 打开成功
2025-08-03 16:16:24 [INFO] OpenForm: 窗体标题已设置为类名 '批量查找'
2025-08-03 16:16:24 [INFO] OpenForm: 准备打开窗体 '批量查找'，位置: Right，单实例: True
2025-08-03 16:16:24 [INFO] 开始显示窗体 '批量查找'，位置模式: Right
2025-08-03 16:16:25 [INFO] 窗体 '批量查找' 以TopMostForm为父窗体显示
2025-08-03 16:16:25 [INFO] 窗体 '批量查找' 显示完成，句柄: 2695814
2025-08-03 16:16:25 [INFO] OpenForm: 窗体 '批量查找' 打开成功
2025-08-03 16:18:12 [INFO] Excel窗口句柄监控器初始化完成
2025-08-03 16:18:13 [INFO] 配置文件实例已在加载时初始化
2025-08-03 16:18:13 [INFO] 开始保存原始控件标题（避免后续被混淆）
2025-08-03 16:18:13 [WARN] UI权限管理器未初始化，无法保存原始控件标题
2025-08-03 16:18:13 [INFO] Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题
2025-08-03 16:18:13 [INFO] 成功初始化Excel应用程序实例
2025-08-03 16:18:13 [INFO] 自动备份路径未配置
2025-08-03 16:18:13 [DEBUG] 开始初始化授权控制器
2025-08-03 16:18:13 [DEBUG] 授权系统初始化完成，耗时: 381ms
2025-08-03 16:18:13 [DEBUG] 开始初始化授权验证
2025-08-03 16:18:13 [INFO] 全局映射管理器已设置: HyControlMappingManager
2025-08-03 16:18:13 [DEBUG] 权限管理器初始化成功
2025-08-03 16:18:13 [DEBUG] 使用新的权限管理器进行初始化
2025-08-03 16:18:13 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-03 16:18:13 [INFO] 开始初始化UI权限管理
2025-08-03 16:18:13 [DEBUG] [实例ID: 423c0b22] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-03 16:18:13 [DEBUG] 🔍 [实例ID: 423c0b22] 字典引用一致性检查:
2025-08-03 16:18:13 [DEBUG] 🔍   标题映射一致性: True
2025-08-03 16:18:13 [DEBUG] 🔍   权限映射一致性: True
2025-08-03 16:18:13 [DEBUG] 🔍   信息映射一致性: True
2025-08-03 16:18:13 [DEBUG] 🔍   特殊控件一致性: True
2025-08-03 16:18:13 [DEBUG] 控件权限管理器初始化完成 [实例ID: 423c0b22]
2025-08-03 16:18:13 [DEBUG] 开始注册控件权限映射
2025-08-03 16:18:13 [INFO] 开始初始化全局控件映射
2025-08-03 16:18:13 [DEBUG] 开始动态生成控件标题映射（从原始控件获取，避免硬编码）
2025-08-03 16:18:13 [DEBUG] 开始生成控件标题映射
2025-08-03 16:18:13 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-08-03 16:18:13 [DEBUG] 通过反射获取到 125 个字段
2025-08-03 16:18:13 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-08-03 16:18:13 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-08-03 16:18:13 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-08-03 16:18:13 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-08-03 16:18:13 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-08-03 16:18:13 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [INFO] 控件结构获取完成，共获取到 123 个控件
2025-08-03 16:18:13 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-03 16:18:13 [INFO] 🔍 处理znAbout控件标题映射: znAboutGroup, Label: '授权', IsEmpty: False
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件 znAboutGroup 标题映射已添加: '授权'
2025-08-03 16:18:13 [INFO] 🔍 处理znAbout控件标题映射: znAboutButton, Label: '授权', IsEmpty: False
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件 znAboutButton 标题映射已添加: '授权'
2025-08-03 16:18:13 [INFO] 🔍 处理znAbout控件标题映射: znAbout, Label: 'ZnAbout', IsEmpty: False
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件 znAbout 标题映射已添加: 'ZnAbout'
2025-08-03 16:18:13 [INFO] 控件标题映射生成完成，共生成 110 项映射
2025-08-03 16:18:13 [INFO] 🔍 最终标题映射中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-03 16:18:13 [DEBUG] 全局控件标题映射生成完成，共生成 110 项
2025-08-03 16:18:13 [INFO] 关键控件标题映射: hyTab -> Develop
2025-08-03 16:18:13 [INFO] 关键控件标题映射: znTab -> ZnTools
2025-08-03 16:18:13 [WARN] 关键控件未找到标题映射: buttonAbout
2025-08-03 16:18:13 [INFO] 关键控件标题映射: znAbout -> ZnAbout
2025-08-03 16:18:13 [INFO] 关键控件标题映射: znAboutGroup -> 授权
2025-08-03 16:18:13 [INFO] 关键控件标题映射: znAboutButton -> 授权
2025-08-03 16:18:13 [INFO] === znAbout控件标题映射诊断 ===
2025-08-03 16:18:13 [INFO] ✓ znAbout 标题映射存在: 'ZnAbout'
2025-08-03 16:18:13 [INFO] ✓ znAboutGroup 标题映射存在: '授权'
2025-08-03 16:18:13 [INFO] ✓ znAboutButton 标题映射存在: '授权'
2025-08-03 16:18:13 [DEBUG] === 所有生成的控件标题映射 ===
2025-08-03 16:18:13 [DEBUG] 控件映射: btm工作表管理 -> '工作表管理'
2025-08-03 16:18:13 [DEBUG] 控件映射: btnAngleExtractor -> '方向角/下倾角提取'
2025-08-03 16:18:13 [DEBUG] 控件映射: btnStationConverter -> '站点系统数量统计'
2025-08-03 16:18:13 [DEBUG] 控件映射: btnStationDataProcessor -> '基站台账数据转换处理'
2025-08-03 16:18:13 [DEBUG] 控件映射: btnTowerAccountProcessor -> '铁塔内部台账转换工具'
2025-08-03 16:18:13 [DEBUG] 控件映射: btn标记提取规整字符串a -> '标记/提取/规整字符'
2025-08-03 16:18:13 [DEBUG] 控件映射: btn标记提取规整字符串b -> '标记/提取/规整字符'
2025-08-03 16:18:13 [DEBUG] 控件映射: btn发送及存档 -> '临时/发送/存档'
2025-08-03 16:18:13 [DEBUG] 控件映射: btn格式化经纬度 -> '经纬度工具'
2025-08-03 16:18:13 [DEBUG] 控件映射: btn金额转大写 -> '金额转大写'
2025-08-03 16:18:13 [DEBUG] 控件映射: btn批量查找 -> '批量查找'
2025-08-03 16:18:13 [DEBUG] 控件映射: btn设置倍数行高 -> '设置倍数行高'
2025-08-03 16:18:13 [DEBUG] 控件映射: btn设置页眉脚 -> '设置页眉脚'
2025-08-03 16:18:13 [DEBUG] 控件映射: btn填写合规检查 -> '填写合规性检查'
2025-08-03 16:18:13 [DEBUG] 控件映射: btn填写合规性检查abc -> '填写合规性检查'
2025-08-03 16:18:13 [DEBUG] 控件映射: btn隐藏范围外内容 -> '隐藏选区外'
2025-08-03 16:18:13 [DEBUG] 控件映射: btn自动脚本 -> '自动脚本'
2025-08-03 16:18:13 [DEBUG] 控件映射: button1 -> 'ini配置文件'
2025-08-03 16:18:13 [DEBUG] 控件映射: button10 -> '记录当前文件'
2025-08-03 16:18:13 [DEBUG] 控件映射: button11 -> '删除外部链接'
2025-08-03 16:18:13 [DEBUG] 控件映射: button12 -> '设置页眉脚'
2025-08-03 16:18:13 [DEBUG] 控件映射: button13 -> '设置倍数行高'
2025-08-03 16:18:13 [DEBUG] 控件映射: button14 -> '临时/发送/存档'
2025-08-03 16:18:13 [DEBUG] 控件映射: button15 -> '订单文件生成kml图层'
2025-08-03 16:18:13 [DEBUG] 控件映射: button16 -> '批量查找站点'
2025-08-03 16:18:13 [DEBUG] 控件映射: button17 -> '向下填充'
2025-08-03 16:18:13 [DEBUG] 控件映射: button18 -> '打开文件'
2025-08-03 16:18:13 [DEBUG] 控件映射: button2 -> 'Excel修复'
2025-08-03 16:18:13 [DEBUG] 控件映射: button20 -> 'Excel修复'
2025-08-03 16:18:13 [DEBUG] 控件映射: button23 -> '生成地理图层'
2025-08-03 16:18:13 [DEBUG] 控件映射: button24 -> '格式化经纬度'
2025-08-03 16:18:13 [DEBUG] 控件映射: button26 -> '重置单元格备注大小'
2025-08-03 16:18:13 [DEBUG] 控件映射: button3 -> '关于'
2025-08-03 16:18:13 [DEBUG] 控件映射: button4 -> '打开配置目录'
2025-08-03 16:18:13 [DEBUG] 控件映射: button5 -> '最近打开文件'
2025-08-03 16:18:13 [DEBUG] 控件映射: button51ToolsV1 -> '51助手'
2025-08-03 16:18:13 [DEBUG] 控件映射: button51ToolsV1b -> '51助手'
2025-08-03 16:18:13 [DEBUG] 控件映射: button51ToolsV2b -> '51小工具v2'
2025-08-03 16:18:13 [DEBUG] 控件映射: button6 -> 'Excel修复'
2025-08-03 16:18:13 [DEBUG] 控件映射: button7 -> 'Wps/Excel切换'
2025-08-03 16:18:13 [DEBUG] 控件映射: button8 -> '订单文件生成kml图层'
2025-08-03 16:18:13 [DEBUG] 控件映射: button9 -> '最近打开文件'
2025-08-03 16:18:13 [DEBUG] 控件映射: buttonAboutHy -> '关于'
2025-08-03 16:18:13 [DEBUG] 控件映射: buttonAboutZn -> '关于'
2025-08-03 16:18:13 [DEBUG] 控件映射: buttonAI辅助填写 -> 'AI辅助填写'
2025-08-03 16:18:13 [DEBUG] 控件映射: buttonDevelopTest -> 'Test'
2025-08-03 16:18:13 [DEBUG] 控件映射: buttonini配置文件 -> 'ini配置文件'
2025-08-03 16:18:13 [DEBUG] 控件映射: buttonPPTHelper -> 'PPT助手'
2025-08-03 16:18:13 [DEBUG] 控件映射: buttonPPT生成修改转PDF_B -> 'PPT批量生成/修改/转PDF'
2025-08-03 16:18:13 [DEBUG] 控件映射: buttonVisioHelper -> 'Visio助手'
2025-08-03 16:18:13 [DEBUG] 控件映射: buttonWordHelper -> 'Word助手'
2025-08-03 16:18:13 [DEBUG] 控件映射: buttonWord生成修改转PDF_B -> 'Word批量生成/修改/转PDF'
2025-08-03 16:18:13 [DEBUG] 控件映射: buttonWpsExcel切换 -> 'Wps/Excel切换'
2025-08-03 16:18:13 [DEBUG] 控件映射: button标签填写筛选 -> '标签填写/筛选'
2025-08-03 16:18:13 [DEBUG] 控件映射: button打开脚本表 -> '打开脚本'
2025-08-03 16:18:13 [DEBUG] 控件映射: button打开文件 -> '打开文件'
2025-08-03 16:18:13 [DEBUG] 控件映射: button多边形GPS坐标转换器 -> '多边形GPS坐标转换器'
2025-08-03 16:18:13 [DEBUG] 控件映射: button复制当前文件路径 -> '复制路径'
2025-08-03 16:18:13 [DEBUG] 控件映射: button记录当前文件 -> '记录当前文件'
2025-08-03 16:18:13 [DEBUG] 控件映射: button考勤 -> '考勤'
2025-08-03 16:18:13 [DEBUG] 控件映射: button配置目录 -> '打开配置目录'
2025-08-03 16:18:13 [DEBUG] 控件映射: button批量找文件 -> '文件查找/复制/改名'
2025-08-03 16:18:13 [DEBUG] 控件映射: button清除全表条件格式 -> '清除全表条件格式'
2025-08-03 16:18:13 [DEBUG] 控件映射: button清除所选条件格式 -> '清除所选条件格式'
2025-08-03 16:18:13 [DEBUG] 控件映射: button取消条件格式并取消筛选 -> '清除所选条件格式及筛选'
2025-08-03 16:18:13 [DEBUG] 控件映射: button生成地理图层 -> '生成地理图层'
2025-08-03 16:18:13 [DEBUG] 控件映射: button铁塔KML点图转换 -> '铁塔KML点图转换'
2025-08-03 16:18:13 [DEBUG] 控件映射: button通过GPS计算最近站点 -> '批量查找站点'
2025-08-03 16:18:13 [DEBUG] 控件映射: button同步数据 -> '同步数据'
2025-08-03 16:18:13 [DEBUG] 控件映射: button外部链接 -> '删除外部链接'
2025-08-03 16:18:13 [DEBUG] 控件映射: button文件操作 -> '文件查找/复制/改名'
2025-08-03 16:18:13 [DEBUG] 控件映射: button向下填充 -> '向下填充'
2025-08-03 16:18:13 [DEBUG] 控件映射: button重置单元格备注大小 -> '重置单元格备注大小'
2025-08-03 16:18:13 [DEBUG] 控件映射: checkBoxHorizontalHighlight -> '水平高亮行列'
2025-08-03 16:18:13 [DEBUG] 控件映射: checkBoxStockHelper -> 'StockHelper'
2025-08-03 16:18:13 [DEBUG] 控件映射: checkBoxVerticalHighlight -> '垂直高亮行列'
2025-08-03 16:18:13 [DEBUG] 控件映射: checkBox叠加显示辅助 -> '叠加显示辅助'
2025-08-03 16:18:13 [DEBUG] 控件映射: checkBox分级标记 -> '分级标记'
2025-08-03 16:18:13 [DEBUG] 控件映射: checkBox监控剪贴板 -> '监控剪贴板'
2025-08-03 16:18:13 [DEBUG] 控件映射: chk显示0值 -> '显示0值'
2025-08-03 16:18:13 [DEBUG] 控件映射: gallery常用文件 -> '常用文件'
2025-08-03 16:18:13 [DEBUG] 控件映射: gallery脚本内容 -> '脚本内容'
2025-08-03 16:18:13 [DEBUG] 控件映射: group1 -> '关于'
2025-08-03 16:18:13 [DEBUG] 控件映射: group2 -> '脚本'
2025-08-03 16:18:13 [DEBUG] 控件映射: groupOffice -> 'Office'
2025-08-03 16:18:13 [DEBUG] 控件映射: group标记标签 -> '标记标签'
2025-08-03 16:18:13 [DEBUG] 控件映射: group数据处理 -> '数据处理'
2025-08-03 16:18:13 [DEBUG] 控件映射: group文件 -> '文件'
2025-08-03 16:18:13 [DEBUG] 控件映射: group无线 -> '无线'
2025-08-03 16:18:13 [DEBUG] 控件映射: group字符格式 -> '字符/格式'
2025-08-03 16:18:13 [DEBUG] 控件映射: hy_group其它 -> '其它'
2025-08-03 16:18:13 [DEBUG] 控件映射: hy_menu设置 -> '设置'
2025-08-03 16:18:13 [DEBUG] 控件映射: hyTab -> 'Develop'
2025-08-03 16:18:13 [DEBUG] 控件映射: menu1 -> '基站数据处理'
2025-08-03 16:18:13 [DEBUG] 控件映射: menu2 -> '其它'
2025-08-03 16:18:13 [DEBUG] 控件映射: menu3 -> '设置'
2025-08-03 16:18:13 [DEBUG] 控件映射: menu5 -> '修复'
2025-08-03 16:18:13 [DEBUG] 控件映射: menuHY -> '其它'
2025-08-03 16:18:13 [DEBUG] 控件映射: menu其它3 -> '其它'
2025-08-03 16:18:13 [DEBUG] 控件映射: menu设置其它 -> '其它'
2025-08-03 16:18:13 [DEBUG] 控件映射: menu修复 -> '修复'
2025-08-03 16:18:13 [DEBUG] 控件映射: zn_groupOffice -> 'Office'
2025-08-03 16:18:13 [DEBUG] 控件映射: zn_group其它 -> '其它'
2025-08-03 16:18:13 [DEBUG] 控件映射: zn_group文件 -> '文件'
2025-08-03 16:18:13 [DEBUG] 控件映射: zn_group无线 -> '无线'
2025-08-03 16:18:13 [DEBUG] 控件映射: zn_group字符格式 -> '字符/格式'
2025-08-03 16:18:13 [DEBUG] 控件映射: znAbout -> 'ZnAbout'
2025-08-03 16:18:13 [DEBUG] 控件映射: znAboutButton -> '授权'
2025-08-03 16:18:13 [DEBUG] 控件映射: znAboutGroup -> '授权'
2025-08-03 16:18:13 [DEBUG] 控件映射: znTab -> 'ZnTools'
2025-08-03 16:18:13 [DEBUG] 获取到权限UI映射: 2 个权限组
2025-08-03 16:18:13 [DEBUG] 开始动态生成控件权限映射（全局一次性创建）
2025-08-03 16:18:13 [DEBUG] 开始生成控件权限映射
2025-08-03 16:18:13 [DEBUG] 开始获取控件结构，容器类型: HyRibbonClass
2025-08-03 16:18:13 [DEBUG] 通过反射获取到 125 个字段
2025-08-03 16:18:13 [INFO] 发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGallery -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonCheckBox -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [INFO] 🔍 处理znAbout控件: znAboutGroup, 类型: RibbonGroup
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonGroup -> IsRibbonControl: True
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件 znAboutGroup 实例获取成功
2025-08-03 16:18:13 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonGroupImpl
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件 znAboutGroup 信息创建成功，Label: '授权'
2025-08-03 16:18:13 [INFO] 🔍 处理znAbout控件: znAboutButton, 类型: RibbonButton
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件 znAboutButton 实例获取成功
2025-08-03 16:18:13 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonButtonImpl
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件Label属性值: '授权'
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件 znAboutButton 信息创建成功，Label: '授权'
2025-08-03 16:18:13 [INFO] 🔍 处理znAbout控件: znAbout, 类型: RibbonTab
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonTab -> IsRibbonControl: True
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件 znAbout 实例获取成功
2025-08-03 16:18:13 [INFO] 🔍 获取znAbout控件标签，类型: Microsoft.Office.Tools.Ribbon.RibbonTabImpl
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件Label属性值: 'ZnAbout'
2025-08-03 16:18:13 [INFO] 🔍 znAbout控件 znAbout 信息创建成功，Label: 'ZnAbout'
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonMenu -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonSeparator -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [DEBUG] 🔍 类型检查: Microsoft.Office.Tools.Ribbon.RibbonButton -> IsRibbonControl: True
2025-08-03 16:18:13 [INFO] 控件结构获取完成，共获取到 123 个控件
2025-08-03 16:18:13 [INFO] 最终结果中包含 3 个znAbout控件: [znAboutGroup='授权', znAboutButton='授权', znAbout='ZnAbout']
2025-08-03 16:18:13 [INFO] 控件权限映射生成完成，共生成 117 项映射
2025-08-03 16:18:13 [DEBUG] 全局控件权限映射生成完成，共生成 117 项
2025-08-03 16:18:13 [INFO] 关键控件权限映射: hyTab -> hyex_dev
2025-08-03 16:18:13 [INFO] 关键控件权限映射: znTab -> hyex_user
2025-08-03 16:18:13 [INFO] 全局控件映射初始化完成 - 标题映射: 110 项, 权限映射: 117 项
2025-08-03 16:18:13 [DEBUG] 批量注册控件权限映射完成，成功: 117/117
2025-08-03 16:18:13 [DEBUG] HyExcel控件权限映射注册完成，共注册 117 个控件
2025-08-03 16:18:13 [INFO] 开始初始化权限验证
2025-08-03 16:18:13 [DEBUG] 设置默认UI可见性为false
2025-08-03 16:18:13 [DEBUG] 开始检查所有需要的权限
2025-08-03 16:18:14 [WARN] 本地授权文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\license.dat
2025-08-03 16:18:14 [INFO] 启动网络授权信息获取任务
2025-08-03 16:18:14 [INFO] 授权信息刷新成功，版本: 1.0, 颁发者: ExtensionsTools
2025-08-03 16:18:14 [INFO] 所有权限检查完成
2025-08-03 16:18:14 [DEBUG] 应用权限状态到UI控件
2025-08-03 16:18:14 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:18:14 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:18:14 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:18:14 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:18:14 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:18:14 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:18:14 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:18:14 [DEBUG] 启动后台权限刷新任务
2025-08-03 16:18:14 [DEBUG] 启动延迟权限刷新任务
2025-08-03 16:18:14 [INFO] 权限验证初始化完成
2025-08-03 16:18:14 [INFO] UI权限管理初始化完成
2025-08-03 16:18:14 [INFO] 收到权限管理器初始化完成通知
2025-08-03 16:18:14 [INFO] 开始刷新控件标题
2025-08-03 16:18:14 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 16:18:14 [DEBUG] 控件权限状态刷新完成，已检查 117 个控件
2025-08-03 16:18:14 [DEBUG] 控件标题刷新完成
2025-08-03 16:18:14 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 16:18:14 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 16:18:14 [DEBUG] 🔍 HyRibbon反射获取到 125 个字段
2025-08-03 16:18:14 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:18:14 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 16:18:14 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 16:18:14 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 16:18:14 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 16:18:14 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 16:18:14 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 16:18:14 [INFO] 动态获取到 123 个Ribbon控件引用
2025-08-03 16:18:14 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:18:14 [INFO] 开始批量更新控件标题，共 123 个控件
2025-08-03 16:18:14 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 16:18:14 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 16:18:14 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 16:18:14 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 16:18:14 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 16:18:14 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 16:18:14 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 16:18:14 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 16:18:14 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 16:18:14 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 16:18:14 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 16:18:14 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 16:18:14 [INFO] 批量更新控件标题完成，成功更新 110 个控件
2025-08-03 16:18:14 [INFO] 动态批量更新完成，共更新 123 个控件
2025-08-03 16:18:14 [INFO] 控件标题更正完成
2025-08-03 16:18:14 [INFO] 控件标题刷新完成
2025-08-03 16:18:14 [INFO] 权限管理器初始化完成处理结束
2025-08-03 16:18:14 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-03 16:18:14 [DEBUG] 授权验证初始化完成
2025-08-03 16:18:14 [INFO] 模板文件不存在: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.template\hyExcelDnaData.xlsx
2025-08-03 16:18:14 [INFO] 成功加载配置和授权信息
2025-08-03 16:18:14 [INFO] 开始初始化定时器和设置
2025-08-03 16:18:14 [INFO] 定时器和设置初始化完成
2025-08-03 16:18:14 [INFO] 开始VSTO插件启动流程
2025-08-03 16:18:14 [INFO] TopMostForm窗体加载完成
2025-08-03 16:18:14 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:18:14 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1779354
2025-08-03 16:18:14 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1779354)
2025-08-03 16:18:14 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 1779354
2025-08-03 16:18:14 [INFO] 系统事件监控已启动
2025-08-03 16:18:14 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:18:14 [INFO] OpenForm: 窗体标题已设置为类名 'CrosshairOverlayForm'
2025-08-03 16:18:14 [INFO] OpenForm: 准备打开窗体 'CrosshairOverlayForm'，位置: Outside，单实例: True
2025-08-03 16:18:14 [INFO] 开始显示窗体 'CrosshairOverlayForm'，位置模式: Outside
2025-08-03 16:18:14 [INFO] 窗体 'CrosshairOverlayForm' 以TopMostForm为父窗体显示
2025-08-03 16:18:14 [INFO] 窗体 'CrosshairOverlayForm' 显示完成，句柄: 11477834
2025-08-03 16:18:14 [INFO] OpenForm: 窗体 'CrosshairOverlayForm' 打开成功
2025-08-03 16:18:14 [INFO] VSTO插件启动流程完成
2025-08-03 16:18:15 [INFO] 从Remote成功获取到网络授权信息
2025-08-03 16:18:15 [INFO] 网络授权信息已更新并触发回调
2025-08-03 16:18:15 [INFO] 网络授权信息已从 Network 更新
2025-08-03 16:18:15 [INFO] 授权版本: 1.0
2025-08-03 16:18:15 [INFO] 颁发者: ExtensionsTools
2025-08-03 16:18:15 [INFO] 用户数量: 3
2025-08-03 16:18:15 [INFO] 分组权限数量: 2
2025-08-03 16:18:15 [WARN] 配置文件中未找到用户组信息
2025-08-03 16:18:15 [INFO] 已重新设置用户组: []
2025-08-03 16:18:15 [INFO] 用户组信息已重新设置
2025-08-03 16:18:15 [INFO] 立即刷新权限缓存和UI界面
2025-08-03 16:18:15 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-03 16:18:15 [DEBUG] 使用新的权限管理器进行强制刷新
2025-08-03 16:18:15 [DEBUG] 开始强制刷新HyExcel权限缓存和UI界面
2025-08-03 16:18:15 [INFO] 开始强制刷新权限缓存和UI界面
2025-08-03 16:18:15 [DEBUG] 本地权限缓存已清空
2025-08-03 16:18:15 [DEBUG] 跳过 LicenseController 刷新，避免死循环
2025-08-03 16:18:15 [INFO] 所有权限检查完成
2025-08-03 16:18:15 [DEBUG] 权限重新检查完成
2025-08-03 16:18:15 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:18:15 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:18:15 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:18:15 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:18:15 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:18:15 [INFO] UI界面权限状态已更新
2025-08-03 16:18:15 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 16:18:15 [DEBUG] 控件权限状态刷新完成，已检查 117 个控件
2025-08-03 16:18:15 [DEBUG] HyExcel权限缓存和UI界面强制刷新完成
2025-08-03 16:18:15 [INFO] 权限缓存和UI界面立即刷新完成
2025-08-03 16:18:15 [INFO] 网络授权已更新，开始刷新控件标题
2025-08-03 16:18:15 [INFO] 开始刷新Ribbon控件标题
2025-08-03 16:18:15 [DEBUG] 权限缓存已清空，清除了 117 个缓存项
2025-08-03 16:18:15 [DEBUG] 开始刷新HyExcel Ribbon控件标题
2025-08-03 16:18:15 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 16:18:15 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 16:18:15 [DEBUG] 🔍 HyRibbon反射获取到 125 个字段
2025-08-03 16:18:15 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:18:15 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 16:18:15 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 16:18:15 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 16:18:15 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 16:18:15 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 16:18:15 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 16:18:15 [INFO] 动态获取到 123 个Ribbon控件引用
2025-08-03 16:18:15 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:18:15 [INFO] 开始批量更新控件标题，共 123 个控件
2025-08-03 16:18:15 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 16:18:15 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 16:18:15 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 16:18:15 [INFO] 批量更新控件标题完成，成功更新 110 个控件
2025-08-03 16:18:15 [INFO] 动态批量更新完成，共更新 123 个控件
2025-08-03 16:18:15 [INFO] 控件标题更正完成
2025-08-03 16:18:15 [DEBUG] HyExcel Ribbon控件标题刷新完成
2025-08-03 16:18:15 [INFO] Ribbon控件标题刷新完成
2025-08-03 16:18:15 [INFO] 控件标题刷新完成
2025-08-03 16:18:15 [DEBUG] Ribbon控件标题已刷新
2025-08-03 16:18:15 [INFO] 开始刷新控件标题
2025-08-03 16:18:15 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 16:18:15 [DEBUG] 控件权限状态刷新完成，已检查 117 个控件
2025-08-03 16:18:15 [DEBUG] 控件标题刷新完成
2025-08-03 16:18:15 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 16:18:15 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 16:18:15 [DEBUG] 🔍 HyRibbon反射获取到 125 个字段
2025-08-03 16:18:15 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:18:15 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 16:18:15 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 16:18:15 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 16:18:15 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 16:18:15 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 16:18:15 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 16:18:15 [INFO] 动态获取到 123 个Ribbon控件引用
2025-08-03 16:18:15 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:18:15 [INFO] 开始批量更新控件标题，共 123 个控件
2025-08-03 16:18:15 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 16:18:15 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 16:18:15 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 16:18:15 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 16:18:15 [INFO] 批量更新控件标题完成，成功更新 110 个控件
2025-08-03 16:18:15 [INFO] 动态批量更新完成，共更新 123 个控件
2025-08-03 16:18:15 [INFO] 控件标题更正完成
2025-08-03 16:18:15 [INFO] 控件标题刷新完成
2025-08-03 16:18:15 [DEBUG] Ribbon控件标题已立即刷新
2025-08-03 16:18:15 [INFO] 开始刷新授权状态
2025-08-03 16:18:15 [DEBUG] 开始初始化授权验证
2025-08-03 16:18:15 [DEBUG] 使用新的权限管理器进行初始化
2025-08-03 16:18:15 [DEBUG] 开始初始化HyExcel UI权限管理器
2025-08-03 16:18:15 [INFO] 开始初始化UI权限管理
2025-08-03 16:18:15 [DEBUG] [实例ID: 73a8d52c] 永远有权限的特殊控件初始化完成，当前数量: 0
2025-08-03 16:18:15 [DEBUG] 🔍 [实例ID: 73a8d52c] 字典引用一致性检查:
2025-08-03 16:18:15 [DEBUG] 🔍   标题映射一致性: True
2025-08-03 16:18:15 [DEBUG] 🔍   权限映射一致性: True
2025-08-03 16:18:15 [DEBUG] 🔍   信息映射一致性: True
2025-08-03 16:18:15 [DEBUG] 🔍   特殊控件一致性: True
2025-08-03 16:18:15 [DEBUG] 控件权限管理器初始化完成 [实例ID: 73a8d52c]
2025-08-03 16:18:15 [DEBUG] 开始注册控件权限映射
2025-08-03 16:18:15 [DEBUG] 批量注册控件权限映射完成，成功: 117/117
2025-08-03 16:18:15 [DEBUG] HyExcel控件权限映射注册完成，共注册 117 个控件
2025-08-03 16:18:15 [INFO] 开始初始化权限验证
2025-08-03 16:18:15 [DEBUG] 设置默认UI可见性为false
2025-08-03 16:18:16 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:18:16 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 1779354
2025-08-03 16:18:16 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 1779354)
2025-08-03 16:18:16 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:18:16 [INFO] Application_WindowResize: 已重新验证TopForm父子关系
2025-08-03 16:18:16 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:18:16 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:18:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:18:16 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:18:16 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:18:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:18:16 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:18:16 [DEBUG] 开始重置 208 个命令栏
2025-08-03 16:18:16 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:18:16 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:18:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:18:16 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:18:16 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:18:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:18:16 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:18:16 [DEBUG] 开始检查所有需要的权限
2025-08-03 16:18:16 [INFO] 所有权限检查完成
2025-08-03 16:18:16 [DEBUG] 应用权限状态到UI控件
2025-08-03 16:18:16 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:18:16 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:18:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:18:16 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:18:16 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:18:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:18:16 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:18:16 [DEBUG] 启动后台权限刷新任务
2025-08-03 16:18:16 [DEBUG] 启动延迟权限刷新任务
2025-08-03 16:18:16 [INFO] 权限验证初始化完成
2025-08-03 16:18:16 [INFO] UI权限管理初始化完成
2025-08-03 16:18:16 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:18:16 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:18:16 [INFO] 收到权限管理器初始化完成通知
2025-08-03 16:18:16 [INFO] 开始刷新控件标题
2025-08-03 16:18:16 [DEBUG] 开始刷新所有控件权限状态
2025-08-03 16:18:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:18:16 [DEBUG] 控件权限状态刷新完成，已检查 117 个控件
2025-08-03 16:18:16 [DEBUG] 控件标题刷新完成
2025-08-03 16:18:16 [INFO] 开始动态更正控件标题（避免硬编码）
2025-08-03 16:18:16 [DEBUG] 开始动态获取Ribbon控件引用
2025-08-03 16:18:16 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:18:16 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:18:16 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:18:16 [DEBUG] 🔍 HyRibbon反射获取到 125 个字段
2025-08-03 16:18:16 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:18:16 [INFO] 🔍 HyRibbon中发现 3 个znAbout相关字段: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:18:16 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutGroup, 类型: Microsoft.Office.Tools.Ribbon.RibbonGroup
2025-08-03 16:18:16 [INFO] 🔍 znAbout字段 znAboutGroup 控件实例获取成功，已添加到引用字典
2025-08-03 16:18:16 [INFO] 🔍 HyRibbon处理znAbout字段: znAboutButton, 类型: Microsoft.Office.Tools.Ribbon.RibbonButton
2025-08-03 16:18:16 [INFO] 🔍 znAbout字段 znAboutButton 控件实例获取成功，已添加到引用字典
2025-08-03 16:18:16 [INFO] 🔍 HyRibbon处理znAbout字段: znAbout, 类型: Microsoft.Office.Tools.Ribbon.RibbonTab
2025-08-03 16:18:16 [INFO] 🔍 znAbout字段 znAbout 控件实例获取成功，已添加到引用字典
2025-08-03 16:18:16 [INFO] 动态获取到 123 个Ribbon控件引用
2025-08-03 16:18:16 [INFO] 🔍 HyRibbon最终控件引用中包含 3 个znAbout控件: [znAboutGroup, znAboutButton, znAbout]
2025-08-03 16:18:16 [INFO] 开始批量更新控件标题，共 123 个控件
2025-08-03 16:18:16 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutGroup
2025-08-03 16:18:16 [DEBUG] 🔍 znAbout控件 znAboutGroup 从全局映射获取到正常标题: '授权'
2025-08-03 16:18:16 [DEBUG] 🔍 znAbout控件 znAboutGroup 权限检查结果: True
2025-08-03 16:18:16 [DEBUG] 🔍 znAbout控件 znAboutGroup 有权限，返回正常标题: '授权'
2025-08-03 16:18:16 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAboutButton
2025-08-03 16:18:16 [DEBUG] 🔍 znAbout控件 znAboutButton 从全局映射获取到正常标题: '授权'
2025-08-03 16:18:16 [DEBUG] 🔍 znAbout控件 znAboutButton 权限检查结果: True
2025-08-03 16:18:16 [DEBUG] 🔍 znAbout控件 znAboutButton 有权限，返回正常标题: '授权'
2025-08-03 16:18:16 [DEBUG] 🔍 开始获取znAbout控件显示标题: znAbout
2025-08-03 16:18:16 [DEBUG] 🔍 znAbout控件 znAbout 从全局映射获取到正常标题: 'ZnAbout'
2025-08-03 16:18:16 [DEBUG] 🔍 znAbout控件 znAbout 权限检查结果: True
2025-08-03 16:18:16 [DEBUG] 🔍 znAbout控件 znAbout 有权限，返回正常标题: 'ZnAbout'
2025-08-03 16:18:16 [INFO] 批量更新控件标题完成，成功更新 110 个控件
2025-08-03 16:18:16 [INFO] 动态批量更新完成，共更新 123 个控件
2025-08-03 16:18:16 [INFO] 控件标题更正完成
2025-08-03 16:18:16 [INFO] 控件标题刷新完成
2025-08-03 16:18:16 [INFO] 权限管理器初始化完成处理结束
2025-08-03 16:18:16 [DEBUG] HyExcel UI权限管理器初始化完成
2025-08-03 16:18:16 [DEBUG] 授权验证初始化完成
2025-08-03 16:18:16 [INFO] 授权状态刷新完成
2025-08-03 16:18:17 [DEBUG] 重置命令栏: cell
2025-08-03 16:18:17 [DEBUG] 重置命令栏: column
2025-08-03 16:18:17 [DEBUG] 重置命令栏: row
2025-08-03 16:18:17 [DEBUG] 重置命令栏: cell
2025-08-03 16:18:17 [DEBUG] 重置命令栏: column
2025-08-03 16:18:17 [DEBUG] 重置命令栏: row
2025-08-03 16:18:17 [DEBUG] 重置命令栏: row
2025-08-03 16:18:17 [DEBUG] 重置命令栏: column
2025-08-03 16:18:18 [DEBUG] 🔍 执行User权限UI操作 - znTab可见性: True
2025-08-03 16:18:18 [DEBUG] 🔍 执行User权限UI操作 - 用户有User权限，隐藏znAbout授权控件
2025-08-03 16:18:18 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:18:18 [DEBUG] 🔍 执行Develop权限UI操作 - hyTab可见性: True
2025-08-03 16:18:18 [DEBUG] 🔍 执行Develop权限UI操作 - 用户有Develop权限，隐藏znAbout授权控件
2025-08-03 16:18:18 [DEBUG] 🔍 znAbout控件隐藏操作完成 - znAbout.Visible=false
2025-08-03 16:18:18 [DEBUG] 已应用权限状态到UI控件
2025-08-03 16:18:21 [INFO] 用户取消了文件选择
2025-08-03 16:18:21 [DEBUG] 命令栏重置完成: 成功 8 个，失败 0 个
2025-08-03 16:18:22 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:18:22 [DEBUG] 授权控制器已初始化
2025-08-03 16:18:22 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:18:23 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:18:24 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:18:24 [DEBUG] 授权控制器已初始化
2025-08-03 16:18:24 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:18:25 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:18:25 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:18:25 [DEBUG] 授权控制器已初始化
2025-08-03 16:18:25 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:18:26 [INFO] 用户取消了文件选择
2025-08-03 16:18:27 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:18:27 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:18:27 [DEBUG] 授权控制器已初始化
2025-08-03 16:18:27 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:18:28 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:18:28 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:18:28 [DEBUG] 授权控制器已初始化
2025-08-03 16:18:28 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:18:29 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:18:29 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:18:29 [DEBUG] 授权控制器已初始化
2025-08-03 16:18:29 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:18:30 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:18:30 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:18:30 [DEBUG] 授权控制器已初始化
2025-08-03 16:18:30 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:18:31 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:18:31 [INFO] 菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:18:31 [DEBUG] 授权控制器已初始化
2025-08-03 16:18:31 [DEBUG] 创建'其它'菜单组，因为用户具有开发权限
2025-08-03 16:18:32 [DEBUG] 创建'常用'菜单组，因为用户具有开发权限
2025-08-03 16:18:32 [DEBUG] 已重置工作表标签菜单
2025-08-03 16:18:32 [DEBUG] 工作表标签菜单权限检查结果: hasProAuthorization = True, 权限键: hyex_dev
2025-08-03 16:18:34 [INFO] OpenForm: 保持窗体现有标题 '存档及发送'
2025-08-03 16:18:34 [INFO] OpenForm: 准备打开窗体 '存档及发送'，位置: Center，单实例: True
2025-08-03 16:18:34 [INFO] 开始显示窗体 '存档及发送'，位置模式: Center
2025-08-03 16:18:34 [INFO] [ET.ETFile] 成功使用 Unicode (UTF-8) 编码读取文件: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\Data\ET\Cache\ETForm\ComboBoxHistory\frm备份及发送_comboBox接收者.data
2025-08-03 16:18:34 [INFO] [ET.ETFile] 成功使用 Unicode (UTF-8) 编码读取文件: D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.data\FileReceiver.txt
2025-08-03 16:18:34 [INFO] 文件接收者配置编辑器初始化完成，配置文件路径：D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\bin\Debug\config\.data\FileReceiver.txt
2025-08-03 16:18:34 [INFO] 窗体 '存档及发送' 以TopMostForm为父窗体显示
2025-08-03 16:18:34 [INFO] 窗体 '存档及发送' 显示完成，句柄: 40246008
2025-08-03 16:18:34 [INFO] OpenForm: 窗体 '存档及发送' 打开成功
2025-08-03 16:18:48 [INFO] OpenForm: 保持窗体现有标题 'Wps Exce 切换'
2025-08-03 16:18:48 [INFO] OpenForm: 准备打开窗体 'Wps Exce 切换'，位置: Center，单实例: True
2025-08-03 16:18:48 [INFO] 开始显示窗体 'Wps Exce 切换'，位置模式: Center
2025-08-03 16:18:48 [INFO] 窗体 '' 以TopMostForm为父窗体显示
2025-08-03 16:18:49 [ERROR] 显示窗体 '' 时发生异常: 无法访问已释放的对象。
对象名:“frmWpsExce切换”。
异常详情：System.ObjectDisposedException: 无法访问已释放的对象。
对象名:“frmWpsExce切换”。
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.Form.CreateHandle()
   在 System.Windows.Forms.Control.get_Handle()
   在 HyExcelVsto.Module.Common.FormManager.ShowForm(Form form, XlFormPosition position) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\FormManager.cs:行号 257
2025-08-03 16:18:49 [ERROR] 窗体操作
异常详情：System.ObjectDisposedException: 无法访问已释放的对象。
对象名:“frmWpsExce切换”。
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.Form.CreateHandle()
   在 System.Windows.Forms.Control.get_Handle()
   在 HyExcelVsto.Module.Common.FormManager.ShowForm(Form form, XlFormPosition position) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\FormManager.cs:行号 257
2025-08-03 16:18:49 [ERROR] 对话框操作
异常详情：ET.ETException: 窗体显示操作失败 ---> System.ObjectDisposedException: 无法访问已释放的对象。
对象名:“frmWpsExce切换”。
   在 System.Windows.Forms.Control.CreateHandle()
   在 System.Windows.Forms.Form.CreateHandle()
   在 System.Windows.Forms.Control.get_Handle()
   在 HyExcelVsto.Module.Common.FormManager.ShowForm(Form form, XlFormPosition position) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\FormManager.cs:行号 257
   --- 内部异常堆栈跟踪的结尾 ---
   在 HyExcelVsto.Module.Common.FormManager.ShowForm(Form form, XlFormPosition position) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\FormManager.cs:行号 273
   在 HyExcelVsto.Module.Common.FormManager.OpenForm(Form form, XlFormPosition position, Boolean singleInstance) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\Module.Common\FormManager.cs:行号 121
   在 HyExcelVsto.ThisAddIn.OpenForm(Form form, XlFormPosition position, Boolean singleInstance) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\ThisAddIn.cs:行号 1327
   在 HyExcelVsto.HyRibbonClass.buttonHyWpsExcel切换_Click(Object sender, RibbonControlEventArgs e) 位置 D:\HyDevelop\HyHelper\HyHelper\HyExcelVsto\HyRibbon.cs:行号 609
2025-08-03 16:22:15 [INFO] 用户取消了文件选择
2025-08-03 16:22:39 [INFO] 检测到剪贴板中的有效Excel文件路径：D:\System\xwechat_files\honghy_eca3\temp\RWTemp\2025-08\e068f1026e56f5faba021abbf1465fef\设计内部台账20250801V1.xlsx
2025-08-03 16:22:41 [INFO] 正在打开Excel文件：D:\System\xwechat_files\honghy_eca3\temp\RWTemp\2025-08\e068f1026e56f5faba021abbf1465fef\设计内部台账20250801V1.xlsx
2025-08-03 16:22:44 [INFO] App_WorkbookOpen: 工作簿 '设计内部台账20250801V1.xlsx' 打开事件触发
2025-08-03 16:22:44 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:22:44 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4530948
2025-08-03 16:22:44 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4530948)
2025-08-03 16:22:44 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:22:44 [INFO] App_WorkbookOpen: 工作簿 '设计内部台账20250801V1.xlsx' 打开处理完成
2025-08-03 16:22:44 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:22:44 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4530948)
2025-08-03 16:22:44 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:22:44 [INFO] App_WorkbookActivate: 工作簿 '设计内部台账20250801V1.xlsx' 激活处理完成
2025-08-03 16:22:44 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:22:44 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4530948)
2025-08-03 16:22:44 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:22:44 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 16:22:44 [INFO] 成功打开Excel文件：D:\System\xwechat_files\honghy_eca3\temp\RWTemp\2025-08\e068f1026e56f5faba021abbf1465fef\设计内部台账20250801V1.xlsx
2025-08-03 16:22:45 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:22:45 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4530948
2025-08-03 16:22:45 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4530948)
2025-08-03 16:22:45 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:22:45 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 16:22:45 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:22:45 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4530948
2025-08-03 16:22:45 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4530948)
2025-08-03 16:22:45 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:22:45 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:22:45 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4530948
2025-08-03 16:22:45 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4530948)
2025-08-03 16:22:45 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:22:45 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-08-03 16:22:56 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-08-03 16:22:56 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-08-03 16:22:56 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-08-03 16:22:56 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl控件初始化完成
2025-08-03 16:22:56 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志级别设置为：Info
2025-08-03 16:22:56 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-08-03 16:22:56 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-08-03 16:22:56 [DEBUG] [ET.Controls.ETLogDisplayControl] 最大日志行数设置为：1000
2025-08-03 16:22:56 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-08-03 16:22:56 [INFO] [ET.Controls.ETLogDisplayControl] 铁塔内部台账梳理功能已就绪
2025-08-03 16:22:56 [INFO] [ET.Controls.ETLogDisplayControl] 铁塔内部台账梳理功能：

1. 铁塔内部台账提取电信部分：
   - 验证工作表类型（必须是'三家运营商会审汇总表'）
   - 复制工作表到新工作簿
   - 删除首行并设置筛选
   - 根据审核人员筛选电信相关数据
   - 按会审日期降序、序号升序排序
   - 设置冻结窗格和筛选行

2. 汇总新铁塔台账到铁塔会审台账：
   - 选择来源表和汇总表
   - 根据会审日期去重汇总数据
   - 复制公式并转换为数值
   - 自动插入新数据到汇总表

注意：本功能针对性强，代码是硬编码，如需改动需直接修改代码
2025-08-03 16:22:56 [INFO] [HyExcelVsto.Module.WX.TowerAccountProcessor_JY.TowerAccountProcessorForm, Text: 铁塔内部台账转换工具] 铁塔台账处理器初始化完成
2025-08-03 16:22:56 [INFO] [HyExcelVsto.Module.WX.TowerAccountProcessor_JY.TowerAccountProcessorForm, Text: 铁塔内部台账转换工具] TowerAccountProcessorForm初始化完成
2025-08-03 16:22:56 [INFO] OpenForm: 保持窗体现有标题 '铁塔内部台账转换工具'
2025-08-03 16:22:56 [INFO] OpenForm: 准备打开窗体 '铁塔内部台账转换工具'，位置: Center，单实例: False
2025-08-03 16:22:56 [INFO] 开始显示窗体 '铁塔内部台账转换工具'，位置模式: Center
2025-08-03 16:22:56 [INFO] 窗体 '铁塔内部台账转换工具' 以TopMostForm为父窗体显示
2025-08-03 16:22:56 [INFO] 窗体 '铁塔内部台账转换工具' 显示完成，句柄: ********
2025-08-03 16:22:56 [INFO] OpenForm: 窗体 '铁塔内部台账转换工具' 打开成功
2025-08-03 16:22:57 [INFO] [ET.Controls.ETLogDisplayControl] [16:22:57] 开始提取铁塔内部台账电信部分...
2025-08-03 16:22:57 [INFO] 开始提取铁塔内部台账电信部分
2025-08-03 16:22:58 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:22:58 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 4530948, 新父窗口: ********
2025-08-03 16:22:58 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: ********)
2025-08-03 16:22:58 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:22:58 [INFO] App_WorkbookActivate: 工作簿 '工作簿2' 激活处理完成
2025-08-03 16:22:58 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:22:58 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: ********)
2025-08-03 16:22:58 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:22:58 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 16:22:58 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:22:59 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:23:01 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: ********
2025-08-03 16:23:01 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: ********)
2025-08-03 16:23:01 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: ********)
2025-08-03 16:23:01 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:23:01 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: ********)
2025-08-03 16:23:01 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:23:01 [INFO] App_WorkbookActivate: 工作簿 '工作簿2' 激活处理完成
2025-08-03 16:23:01 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:23:01 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: ********)
2025-08-03 16:23:01 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:23:01 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 16:23:01 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:23:01 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: ********)
2025-08-03 16:23:01 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:23:01 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:23:01 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:23:06 [INFO] 铁塔内部台账电信部分提取完成，新工作表：2018年7月11日后会审清单
2025-08-03 16:23:06 [INFO] [ET.Controls.ETLogDisplayControl] [16:23:06] ✅ 提取完成！新工作表：2018年7月11日后会审清单（已复制到剪贴板）
2025-08-03 16:23:06 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:23:06 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: ********
2025-08-03 16:23:06 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: ********
2025-08-03 16:23:06 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: ********)
2025-08-03 16:23:06 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: ********)
2025-08-03 16:23:06 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:23:06 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 16:23:06 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:23:06 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:23:06 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 16:23:38 [INFO] [HyExcelVsto.Module.WX.TowerAccountProcessor_JY.TowerAccountProcessorForm, Text: 铁塔内部台账转换工具] TowerAccountProcessorForm资源已释放
2025-08-03 16:23:38 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl正在释放资源
2025-08-03 16:23:58 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-08-03 16:23:58 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-08-03 16:23:58 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-08-03 16:23:58 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl控件初始化完成
2025-08-03 16:23:58 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志级别设置为：Info
2025-08-03 16:23:58 [DEBUG] [ET.Controls.ETLogDisplayControl] ETLogManager注册完成
2025-08-03 16:23:58 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志显示初始化完成
2025-08-03 16:23:58 [DEBUG] [ET.Controls.ETLogDisplayControl] 最大日志行数设置为：1000
2025-08-03 16:23:58 [DEBUG] [ET.Controls.ETLogDisplayControl] 日志自动换行设置为：True
2025-08-03 16:23:58 [INFO] [ET.Controls.ETLogDisplayControl] 铁塔内部台账梳理功能已就绪
2025-08-03 16:23:58 [INFO] [ET.Controls.ETLogDisplayControl] 铁塔内部台账梳理功能：

1. 铁塔内部台账提取电信部分：
   - 验证工作表类型（必须是'三家运营商会审汇总表'）
   - 复制工作表到新工作簿
   - 删除首行并设置筛选
   - 根据审核人员筛选电信相关数据
   - 按会审日期降序、序号升序排序
   - 设置冻结窗格和筛选行

2. 汇总新铁塔台账到铁塔会审台账：
   - 选择来源表和汇总表
   - 根据会审日期去重汇总数据
   - 复制公式并转换为数值
   - 自动插入新数据到汇总表

注意：本功能针对性强，代码是硬编码，如需改动需直接修改代码
2025-08-03 16:23:58 [INFO] [HyExcelVsto.Module.WX.TowerAccountProcessor_JY.TowerAccountProcessorForm, Text: 铁塔内部台账转换工具] 铁塔台账处理器初始化完成
2025-08-03 16:23:58 [INFO] [HyExcelVsto.Module.WX.TowerAccountProcessor_JY.TowerAccountProcessorForm, Text: 铁塔内部台账转换工具] TowerAccountProcessorForm初始化完成
2025-08-03 16:23:58 [INFO] OpenForm: 保持窗体现有标题 '铁塔内部台账转换工具'
2025-08-03 16:23:58 [INFO] OpenForm: 准备打开窗体 '铁塔内部台账转换工具'，位置: Center，单实例: False
2025-08-03 16:23:58 [INFO] 开始显示窗体 '铁塔内部台账转换工具'，位置模式: Center
2025-08-03 16:23:58 [INFO] 窗体 '铁塔内部台账转换工具' 以TopMostForm为父窗体显示
2025-08-03 16:23:58 [INFO] 窗体 '铁塔内部台账转换工具' 显示完成，句柄: 5646458
2025-08-03 16:23:58 [INFO] OpenForm: 窗体 '铁塔内部台账转换工具' 打开成功
2025-08-03 16:24:01 [INFO] [ET.ETForm] 成功加载RibbonGallery配置文件菜单：收藏文件.config，共 12 个配置项
2025-08-03 16:24:05 [INFO] App_WorkbookOpen: 工作簿 '★铁塔会审.xlsx' 打开事件触发
2025-08-03 16:24:05 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:24:05 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: ********, 新父窗口: 13438836
2025-08-03 16:24:05 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13438836)
2025-08-03 16:24:05 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:24:05 [INFO] App_WorkbookOpen: 工作簿 '★铁塔会审.xlsx' 打开处理完成
2025-08-03 16:24:05 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:24:05 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13438836)
2025-08-03 16:24:05 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:24:05 [INFO] App_WorkbookActivate: 工作簿 '★铁塔会审.xlsx' 激活处理完成
2025-08-03 16:24:05 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:24:05 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13438836)
2025-08-03 16:24:05 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:24:05 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 16:24:05 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:24:05 [WARN] 检测到Excel窗口句柄变化: ******** -> 13438836
2025-08-03 16:24:05 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13438836
2025-08-03 16:24:06 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13438836)
2025-08-03 16:24:06 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:24:06 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13438836
2025-08-03 16:24:06 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13438836)
2025-08-03 16:24:06 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:24:06 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 16:24:06 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13438836)
2025-08-03 16:24:06 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:24:06 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:24:06 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13438836
2025-08-03 16:24:06 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13438836)
2025-08-03 16:24:06 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:24:06 [INFO] App_WorkbookOpen: TopForm关系验证完成
2025-08-03 16:24:10 [INFO] [ET.Controls.ETLogDisplayControl] [16:24:10] 开始汇总新铁塔台账到铁塔会审台账...
2025-08-03 16:24:10 [INFO] 开始汇总新铁塔台账到铁塔会审台账
2025-08-03 16:24:20 [INFO] 公式复制和转换完成
2025-08-03 16:24:20 [INFO] 数据汇总完成！新插入了 37 行数据。
2025-08-03 16:24:21 [INFO] [ET.Controls.ETLogDisplayControl] [16:24:21] ✅ 数据汇总完成！新插入了 37 行数据。
2025-08-03 16:24:23 [INFO] [HyExcelVsto.Module.WX.TowerAccountProcessor_JY.TowerAccountProcessorForm, Text: 铁塔内部台账转换工具] TowerAccountProcessorForm资源已释放
2025-08-03 16:24:23 [INFO] [ET.Controls.ETLogDisplayControl] ETLogDisplayControl正在释放资源
2025-08-03 16:25:32 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-08-03 16:25:32 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-08-03 16:25:32 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-08-03 16:25:32 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-08-03 16:25:32 [DEBUG] [ET.Controls.ETRangeSelectControl] 控件设置初始化完成
2025-08-03 16:25:32 [DEBUG] [ET.Controls.ETRangeSelectControl] ETRangeSelectControl初始化完成，使用WPS直接提供者
2025-08-03 16:25:32 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-08-03 16:25:32 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-08-03 16:25:32 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-08-03 16:25:32 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-08-03 16:25:32 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：null
2025-08-03 16:25:32 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：null，触发事件：True
2025-08-03 16:25:32 [INFO] OpenForm: 保持窗体现有标题 'GPS生成KML图层'
2025-08-03 16:25:32 [INFO] OpenForm: 准备打开窗体 'GPS生成KML图层'，位置: Center，单实例: True
2025-08-03 16:25:32 [INFO] 开始显示窗体 'GPS生成KML图层'，位置模式: Center
2025-08-03 16:25:32 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：'会审记录2024'!$K:$L
2025-08-03 16:25:32 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：'会审记录2024'!$K:$L，触发事件：True
2025-08-03 16:25:32 [DEBUG] [ET.Controls.ETRangeSelectControl] 内部触发SelectedEvent事件：'会审记录2024'!$J:$J
2025-08-03 16:25:32 [DEBUG] [ET.Controls.ETRangeSelectControl] 设置选中范围：'会审记录2024'!$J:$J，触发事件：True
2025-08-03 16:25:32 [DEBUG] [ET.Controls.ETRangeSelectControl] 文本框获得焦点，上次地址：'会审记录2024'!$K:$L
2025-08-03 16:25:33 [INFO] 窗体 'GPS生成KML图层' 以TopMostForm为父窗体显示
2025-08-03 16:25:33 [INFO] 窗体 'GPS生成KML图层' 显示完成，句柄: 21897326
2025-08-03 16:25:33 [INFO] OpenForm: 窗体 'GPS生成KML图层' 打开成功
2025-08-03 16:28:31 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:28:31 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 13438836, 新父窗口: ********
2025-08-03 16:28:31 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: ********)
2025-08-03 16:28:31 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:28:31 [INFO] App_WorkbookActivate: 工作簿 '2018年7月11日后会审清单-20250803.xlsx' 激活处理完成
2025-08-03 16:28:31 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:28:31 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: ********)
2025-08-03 16:28:31 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:28:31 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 16:28:32 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:28:32 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: ********
2025-08-03 16:28:32 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: ********)
2025-08-03 16:28:32 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:28:32 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 16:28:32 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:28:32 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: ********
2025-08-03 16:28:32 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: ********)
2025-08-03 16:28:32 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:28:33 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-08-03 16:28:33 [INFO] 系统事件监控已停止
2025-08-03 16:28:33 [INFO] Excel窗口句柄监控已停止
2025-08-03 16:28:33 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-08-03 16:28:33 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:28:33 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13438836
2025-08-03 16:28:33 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13438836)
2025-08-03 16:28:33 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 13438836
2025-08-03 16:28:33 [INFO] 系统事件监控已启动
2025-08-03 16:28:33 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:28:33 [INFO] App_WorkbookActivate: 工作簿 '★铁塔会审.xlsx' 激活处理完成
2025-08-03 16:28:33 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:28:33 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13438836)
2025-08-03 16:28:33 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:28:33 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 16:28:33 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:28:34 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13438836
2025-08-03 16:28:34 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13438836)
2025-08-03 16:28:34 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:28:34 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 16:28:34 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:28:34 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13438836
2025-08-03 16:28:34 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13438836)
2025-08-03 16:28:34 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:28:35 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:28:35 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 13438836, 新父窗口: 4530948
2025-08-03 16:28:35 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4530948)
2025-08-03 16:28:35 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:28:35 [INFO] App_WorkbookActivate: 工作簿 '设计内部台账20250801V1.xlsx' 激活处理完成
2025-08-03 16:28:35 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:28:35 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 4530948)
2025-08-03 16:28:35 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:28:35 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 16:28:35 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:28:35 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4530948
2025-08-03 16:28:35 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4530948)
2025-08-03 16:28:35 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:28:35 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 16:28:35 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:28:36 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 4530948
2025-08-03 16:28:36 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 4530948)
2025-08-03 16:28:36 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:28:56 [INFO] TopMostForm.Stop: 开始停止窗体管理功能
2025-08-03 16:28:56 [INFO] 系统事件监控已停止
2025-08-03 16:28:56 [INFO] Excel窗口句柄监控已停止
2025-08-03 16:28:56 [INFO] TopMostForm.Stop: 窗体管理功能停止完成
2025-08-03 16:28:56 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:28:56 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13438836
2025-08-03 16:28:56 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13438836)
2025-08-03 16:28:56 [INFO] Excel窗口句柄监控已启动，监控间隔: 5000ms，初始句柄: 13438836
2025-08-03 16:28:56 [INFO] 系统事件监控已启动
2025-08-03 16:28:56 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:28:56 [INFO] App_WorkbookActivate: 工作簿 '★铁塔会审.xlsx' 激活处理完成
2025-08-03 16:28:56 [INFO] TopMostForm.Start: 开始启动窗体管理功能
2025-08-03 16:28:56 [INFO] SetTop: 父子窗口关系已正确 (Excel句柄: 13438836)
2025-08-03 16:28:56 [INFO] TopMostForm.Start: 窗体管理功能启动完成
2025-08-03 16:28:56 [INFO] App_WindowActivate: Excel窗口激活处理完成
2025-08-03 16:28:56 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:28:56 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13438836
2025-08-03 16:28:56 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13438836)
2025-08-03 16:28:56 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
2025-08-03 16:28:56 [INFO] App_WorkbookActivate: TopForm关系验证完成
2025-08-03 16:28:56 [INFO] TopMostForm.ForceRestoreParentRelation: 开始强制恢复父子窗口关系
2025-08-03 16:28:57 [INFO] SetTop: 更新父窗口关系 - 当前父窗口: 65552, 新父窗口: 13438836
2025-08-03 16:28:57 [INFO] SetTop: 成功设置父窗口关系 (Excel句柄: 13438836)
2025-08-03 16:28:57 [INFO] TopMostForm.ForceRestoreParentRelation: 强制恢复完成
